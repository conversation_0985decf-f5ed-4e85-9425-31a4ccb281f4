package com.example.springvueapp.config;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.model.McpServerStatus;
import com.example.springvueapp.service.McpSseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * McpRoutingConfig 的单元测试
 * 测试函数式路由配置和处理器
 */
@ExtendWith(MockitoExtension.class)
public class McpRoutingConfigTest {

    @Mock
    private McpSseService mcpSseService;

    private McpRoutingConfig mcpRoutingConfig;
    private WebTestClient webTestClient;

    private static final String TEST_SANDBOX_ID = "test-sandbox-123";
    private static final Long TEST_CONFIG_ID = 100L;
    private static final Long TEST_USER_ID = 1L;

    @BeforeEach
    void setUp() {
        mcpRoutingConfig = new McpRoutingConfig();
        RouterFunction<ServerResponse> routerFunction = mcpRoutingConfig.mcpRoutes(mcpSseService);
        webTestClient = WebTestClient.bindToRouterFunction(routerFunction).build();
    }

    @Test
    void testSseEventsRoute_Success() {
        // 准备测试数据
        Flux<String> mockEventStream = Flux.just("event1", "event2", "event3");
        when(mcpSseService.streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(mockEventStream);

        // 测试SSE事件流路由
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/events", TEST_SANDBOX_ID)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentTypeCompatibleWith(MediaType.TEXT_EVENT_STREAM)
                .expectBodyList(String.class)
                .hasSize(3)
                .contains("event1", "event2", "event3");

        verify(mcpSseService).streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testSseEventsRoute_ServiceError() {
        // 模拟服务抛出异常
        when(mcpSseService.streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Flux.error(new RuntimeException("连接失败")));

        // 测试错误处理
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/events", TEST_SANDBOX_ID)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .exchange()
                .expectStatus().is5xxServerError(); // 服务器内部错误

        verify(mcpSseService).streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testStatusRoute_Success() {
        // 准备测试数据
        McpServerStatus mockStatus = new McpServerStatus(TEST_SANDBOX_ID, true);
        mockStatus.setLastActivity(LocalDateTime.now());
        mockStatus.setActiveConnections(2);

        when(mcpSseService.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(mockStatus));

        // 测试状态查询路由
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/status", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.APPLICATION_JSON)
                .expectBody()
                .jsonPath("$.sandboxId").isEqualTo(TEST_SANDBOX_ID)
                .jsonPath("$.connected").isEqualTo(true)
                .jsonPath("$.activeConnections").isEqualTo(2);

        verify(mcpSseService).getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testStatusRoute_ServiceError() {
        // 模拟服务抛出异常
        when(mcpSseService.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("状态获取失败")));

        // 测试错误处理
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/status", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody(String.class)
                .isEqualTo("获取状态失败: 状态获取失败");

        verify(mcpSseService).getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testPingRoute_Success() {
        // 准备测试数据
        Long mockLatency = 150L;
        when(mcpSseService.pingMcpServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(mockLatency));

        // 测试心跳检测路由
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/ping", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.APPLICATION_JSON)
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.latency").isEqualTo(mockLatency)
                .jsonPath("$.timestamp").exists();

        verify(mcpSseService).pingMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testPingRoute_ServiceError() {
        // 模拟服务抛出异常
        when(mcpSseService.pingMcpServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("Ping失败")));

        // 测试错误处理
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/ping", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error").isEqualTo("Ping失败")
                .jsonPath("$.timestamp").exists();

        verify(mcpSseService).pingMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testSendRoute_Success() {
        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId("req-123");
        request.setMethod("test.method");
        request.setParams(Map.of("param1", "value1"));

        when(mcpSseService.sendMcpRequest(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID), any(JsonRpcRequest.class)))
                .thenReturn(Mono.empty());

        // 测试发送请求路由
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/send", TEST_SANDBOX_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.APPLICATION_JSON)
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.message").isEqualTo("请求已发送");

        verify(mcpSseService).sendMcpRequest(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID), any(JsonRpcRequest.class));
    }

    @Test
    void testSendRoute_ServiceError() {
        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setMethod("test.method");

        when(mcpSseService.sendMcpRequest(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID), any(JsonRpcRequest.class)))
                .thenReturn(Mono.error(new RuntimeException("发送失败")));

        // 测试错误处理
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/send", TEST_SANDBOX_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error").isEqualTo("发送失败");

        verify(mcpSseService).sendMcpRequest(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID), any(JsonRpcRequest.class));
    }

    @Test
    void testServerStartRoute() {
        // 测试服务器启动路由
        webTestClient.post()
                .uri("/api/mcp/servers/{configId}/start", TEST_CONFIG_ID)
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.APPLICATION_JSON)
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.message").isEqualTo("服务器启动请求已提交")
                .jsonPath("$.configId").isEqualTo(TEST_CONFIG_ID);
    }

    @Test
    void testServerStopRoute() {
        // 测试服务器停止路由
        webTestClient.post()
                .uri("/api/mcp/servers/{sandboxId}/stop", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.APPLICATION_JSON)
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.message").isEqualTo("服务器停止请求已提交")
                .jsonPath("$.sandboxId").isEqualTo(TEST_SANDBOX_ID);
    }

    @Test
    void testServerRestartRoute() {
        // 测试服务器重启路由
        webTestClient.post()
                .uri("/api/mcp/servers/{sandboxId}/restart", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.APPLICATION_JSON)
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.message").isEqualTo("服务器重启请求已提交")
                .jsonPath("$.sandboxId").isEqualTo(TEST_SANDBOX_ID);
    }

    @Test
    void testServerListRoute() {
        // 测试获取服务器列表路由
        webTestClient.get()
                .uri("/api/mcp/servers")
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.APPLICATION_JSON)
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.servers").isArray()
                .jsonPath("$.userId").isEqualTo(TEST_USER_ID);
    }

    @Test
    void testHealthRoute() {
        // 测试健康检查路由
        webTestClient.get()
                .uri("/api/mcp/health")
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.APPLICATION_JSON)
                .expectBody()
                .jsonPath("$.status").isEqualTo("UP")
                .jsonPath("$.service").isEqualTo("MCP SSE Service")
                .jsonPath("$.timestamp").exists();
    }

    @Test
    void testInvalidRoute() {
        // 测试不存在的路由
        webTestClient.get()
                .uri("/api/mcp/invalid")
                .exchange()
                .expectStatus().isNotFound();
    }

    @Test
    void testWrongHttpMethod() {
        // 测试错误的HTTP方法
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/ping", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().is4xxClientError();
    }
}
