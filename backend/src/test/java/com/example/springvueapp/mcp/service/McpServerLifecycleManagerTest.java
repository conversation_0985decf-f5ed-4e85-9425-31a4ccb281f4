package com.example.springvueapp.mcp.service;

import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.model.McpServerStatus;
import com.example.springvueapp.service.McpConfigurationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * McpServerLifecycleManager 的单元测试
 * 测试MCP服务器生命周期管理功能
 */
@ExtendWith(MockitoExtension.class)
public class McpServerLifecycleManagerTest {

    @Mock
    private McpServiceManager mcpServiceManager;

    @Mock
    private McpConfigurationService configurationService;

    @InjectMocks
    private McpServerLifecycleManager lifecycleManager;

    private static final String TEST_SANDBOX_ID = "test-sandbox-123";
    private static final Long TEST_USER_ID = 1L;
    private static final Long TEST_CONFIG_ID = 100L;

    private McpServerConfiguration testConfiguration;
    private McpServerInstance testInstance;

    @BeforeEach
    void setUp() {
        // 准备测试配置
        testConfiguration = new McpServerConfiguration();
        testConfiguration.setId(TEST_CONFIG_ID);
        testConfiguration.setUserId(TEST_USER_ID);
        testConfiguration.setName("测试MCP服务器");
        testConfiguration.setEnabled(true);
        testConfiguration.setAutoRestart(true);

        // 准备测试实例
        testInstance = new McpServerInstance();
        testInstance.setSandboxId(TEST_SANDBOX_ID);
        testInstance.setConfigurationId(TEST_CONFIG_ID);
        testInstance.setUserId(TEST_USER_ID);
        testInstance.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void testStartServer_Success() {
        // 准备模拟数据
        when(configurationService.getConfiguration(TEST_CONFIG_ID, TEST_USER_ID))
                .thenReturn(Mono.just(testConfiguration));
        when(mcpServiceManager.startMcpServer(testConfiguration, TEST_USER_ID))
                .thenReturn(Mono.just(testInstance));

        // 调用被测试方法
        Mono<McpServerInstance> result = lifecycleManager.startServer(TEST_CONFIG_ID, TEST_USER_ID);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(instance -> {
                    assertEquals(TEST_SANDBOX_ID, instance.getSandboxId());
                    assertEquals(TEST_CONFIG_ID, instance.getConfigurationId());
                    assertEquals(TEST_USER_ID, instance.getUserId());
                })
                .verifyComplete();

        // 验证服务调用
        verify(configurationService).getConfiguration(TEST_CONFIG_ID, TEST_USER_ID);
        verify(mcpServiceManager).startMcpServer(testConfiguration, TEST_USER_ID);
    }

    @Test
    void testStartServer_ConfigurationNotFound() {
        // 模拟配置不存在
        when(configurationService.getConfiguration(TEST_CONFIG_ID, TEST_USER_ID))
                .thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<McpServerInstance> result = lifecycleManager.startServer(TEST_CONFIG_ID, TEST_USER_ID);

        // 验证错误处理
        StepVerifier.create(result)
                .verifyComplete(); // 空结果

        verify(configurationService).getConfiguration(TEST_CONFIG_ID, TEST_USER_ID);
        verify(mcpServiceManager, never()).startMcpServer(any(), any());
    }

    @Test
    void testStartServer_ConfigurationDisabled() {
        // 准备禁用的配置
        testConfiguration.setEnabled(false);
        when(configurationService.getConfiguration(TEST_CONFIG_ID, TEST_USER_ID))
                .thenReturn(Mono.just(testConfiguration));

        // 调用被测试方法
        Mono<McpServerInstance> result = lifecycleManager.startServer(TEST_CONFIG_ID, TEST_USER_ID);

        // 验证错误处理
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);

        verify(configurationService).getConfiguration(TEST_CONFIG_ID, TEST_USER_ID);
        verify(mcpServiceManager, never()).startMcpServer(any(), any());
    }

    @Test
    void testStartServer_ServiceManagerError() {
        // 准备模拟数据
        when(configurationService.getConfiguration(TEST_CONFIG_ID, TEST_USER_ID))
                .thenReturn(Mono.just(testConfiguration));
        when(mcpServiceManager.startMcpServer(testConfiguration, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("启动失败")));

        // 调用被测试方法
        Mono<McpServerInstance> result = lifecycleManager.startServer(TEST_CONFIG_ID, TEST_USER_ID);

        // 验证错误处理
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);

        verify(configurationService).getConfiguration(TEST_CONFIG_ID, TEST_USER_ID);
        verify(mcpServiceManager).startMcpServer(testConfiguration, TEST_USER_ID);
    }

    @Test
    void testStopServer_Success() {
        // 准备模拟数据
        when(mcpServiceManager.stopMcpServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Void> result = lifecycleManager.stopServer(TEST_SANDBOX_ID, TEST_USER_ID);

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();

        verify(mcpServiceManager).stopMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testStopServer_ServiceError() {
        // 模拟服务抛出异常
        when(mcpServiceManager.stopMcpServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("停止失败")));

        // 调用被测试方法
        Mono<Void> result = lifecycleManager.stopServer(TEST_SANDBOX_ID, TEST_USER_ID);

        // 验证错误处理
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);

        verify(mcpServiceManager).stopMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testRestartServer_Success() {
        // 准备模拟数据
        when(mcpServiceManager.restartMcpServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(testInstance));

        // 调用被测试方法
        Mono<McpServerInstance> result = lifecycleManager.restartServer(TEST_SANDBOX_ID, TEST_USER_ID);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(instance -> {
                    assertEquals(TEST_SANDBOX_ID, instance.getSandboxId());
                    assertEquals(TEST_CONFIG_ID, instance.getConfigurationId());
                })
                .verifyComplete();

        verify(mcpServiceManager).restartMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testRestartServer_ServiceError() {
        // 模拟服务抛出异常
        when(mcpServiceManager.restartMcpServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("重启失败")));

        // 调用被测试方法
        Mono<McpServerInstance> result = lifecycleManager.restartServer(TEST_SANDBOX_ID, TEST_USER_ID);

        // 验证错误处理
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);

        verify(mcpServiceManager).restartMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testGetUserServers_Success() {
        // 准备模拟数据
        McpServerInstance instance1 = new McpServerInstance();
        instance1.setSandboxId("sandbox-1");
        instance1.setConfigurationId(1L);

        McpServerInstance instance2 = new McpServerInstance();
        instance2.setSandboxId("sandbox-2");
        instance2.setConfigurationId(2L);

        when(mcpServiceManager.getUserMcpServers(TEST_USER_ID))
                .thenReturn(Flux.just(instance1, instance2));

        // 调用被测试方法
        Flux<McpServerInstance> result = lifecycleManager.getUserServers(TEST_USER_ID);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(instance -> assertEquals("sandbox-1", instance.getSandboxId()))
                .assertNext(instance -> assertEquals("sandbox-2", instance.getSandboxId()))
                .verifyComplete();

        verify(mcpServiceManager).getUserMcpServers(TEST_USER_ID);
    }

    @Test
    void testGetUserServers_ServiceError() {
        // 模拟服务抛出异常
        when(mcpServiceManager.getUserMcpServers(TEST_USER_ID))
                .thenReturn(Flux.error(new RuntimeException("获取失败")));

        // 调用被测试方法
        Flux<McpServerInstance> result = lifecycleManager.getUserServers(TEST_USER_ID);

        // 验证错误处理
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);

        verify(mcpServiceManager).getUserMcpServers(TEST_USER_ID);
    }

    @Test
    void testGetServerStatus_Success() {
        // 准备模拟数据
        McpServerStatus mockStatus = new McpServerStatus(TEST_SANDBOX_ID, true);
        mockStatus.setLastActivity(LocalDateTime.now());
        mockStatus.setActiveConnections(2);

        when(mcpServiceManager.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(mockStatus));

        // 调用被测试方法
        Mono<McpServerStatus> result = lifecycleManager.getServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(status -> {
                    assertEquals(TEST_SANDBOX_ID, status.getSandboxId());
                    assertTrue(status.isConnected());
                    assertEquals(2, status.getActiveConnections());
                })
                .verifyComplete();

        verify(mcpServiceManager).getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testGetServerStatus_ServiceError() {
        // 模拟服务抛出异常
        when(mcpServiceManager.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("状态获取失败")));

        // 调用被测试方法
        Mono<McpServerStatus> result = lifecycleManager.getServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);

        // 验证错误处理
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);

        verify(mcpServiceManager).getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testStartAllEnabledServers_Success() {
        // 准备测试配置
        McpServerConfiguration config1 = new McpServerConfiguration();
        config1.setId(1L);
        config1.setEnabled(true);
        config1.setUserId(TEST_USER_ID);

        McpServerConfiguration config2 = new McpServerConfiguration();
        config2.setId(2L);
        config2.setEnabled(true);
        config2.setUserId(TEST_USER_ID);

        McpServerConfiguration config3 = new McpServerConfiguration();
        config3.setId(3L);
        config3.setEnabled(false); // 禁用的配置
        config3.setUserId(TEST_USER_ID);

        // 准备测试实例
        McpServerInstance instance1 = new McpServerInstance();
        instance1.setSandboxId("sandbox-1");
        instance1.setConfigurationId(1L);

        McpServerInstance instance2 = new McpServerInstance();
        instance2.setSandboxId("sandbox-2");
        instance2.setConfigurationId(2L);

        // 模拟服务调用
        when(configurationService.getUserConfigurations(TEST_USER_ID))
                .thenReturn(Flux.just(config1, config2, config3));
        when(configurationService.getConfiguration(1L, TEST_USER_ID))
                .thenReturn(Mono.just(config1));
        when(configurationService.getConfiguration(2L, TEST_USER_ID))
                .thenReturn(Mono.just(config2));
        when(mcpServiceManager.startMcpServer(config1, TEST_USER_ID))
                .thenReturn(Mono.just(instance1));
        when(mcpServiceManager.startMcpServer(config2, TEST_USER_ID))
                .thenReturn(Mono.just(instance2));

        // 调用被测试方法
        Flux<McpServerInstance> result = lifecycleManager.startAllEnabledServers(TEST_USER_ID);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(instance -> assertEquals("sandbox-1", instance.getSandboxId()))
                .assertNext(instance -> assertEquals("sandbox-2", instance.getSandboxId()))
                .verifyComplete();

        // 验证只启动了启用的配置
        verify(configurationService).getUserConfigurations(TEST_USER_ID);
        verify(mcpServiceManager).startMcpServer(config1, TEST_USER_ID);
        verify(mcpServiceManager).startMcpServer(config2, TEST_USER_ID);
        verify(mcpServiceManager, never()).startMcpServer(config3, TEST_USER_ID);
    }

    @Test
    void testStartAllEnabledServers_PartialFailure() {
        // 准备测试配置
        McpServerConfiguration config1 = new McpServerConfiguration();
        config1.setId(1L);
        config1.setEnabled(true);
        config1.setUserId(TEST_USER_ID);

        McpServerConfiguration config2 = new McpServerConfiguration();
        config2.setId(2L);
        config2.setEnabled(true);
        config2.setUserId(TEST_USER_ID);

        // 准备测试实例
        McpServerInstance instance1 = new McpServerInstance();
        instance1.setSandboxId("sandbox-1");
        instance1.setConfigurationId(1L);

        // 模拟服务调用
        when(configurationService.getUserConfigurations(TEST_USER_ID))
                .thenReturn(Flux.just(config1, config2));
        when(configurationService.getConfiguration(1L, TEST_USER_ID))
                .thenReturn(Mono.just(config1));
        when(configurationService.getConfiguration(2L, TEST_USER_ID))
                .thenReturn(Mono.just(config2));
        when(mcpServiceManager.startMcpServer(config1, TEST_USER_ID))
                .thenReturn(Mono.just(instance1));
        when(mcpServiceManager.startMcpServer(config2, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("启动失败")));

        // 调用被测试方法
        Flux<McpServerInstance> result = lifecycleManager.startAllEnabledServers(TEST_USER_ID);

        // 验证结果 - 只返回成功启动的实例
        StepVerifier.create(result)
                .assertNext(instance -> assertEquals("sandbox-1", instance.getSandboxId()))
                .verifyComplete();

        verify(configurationService).getUserConfigurations(TEST_USER_ID);
        verify(mcpServiceManager).startMcpServer(config1, TEST_USER_ID);
        verify(mcpServiceManager).startMcpServer(config2, TEST_USER_ID);
    }

    @Test
    void testStopAllUserServers_Success() {
        // 准备测试实例
        McpServerInstance instance1 = new McpServerInstance();
        instance1.setSandboxId("sandbox-1");
        instance1.setUserId(TEST_USER_ID);

        McpServerInstance instance2 = new McpServerInstance();
        instance2.setSandboxId("sandbox-2");
        instance2.setUserId(TEST_USER_ID);

        // 模拟服务调用
        when(mcpServiceManager.getUserMcpServers(TEST_USER_ID))
                .thenReturn(Flux.just(instance1, instance2));
        when(mcpServiceManager.stopMcpServer("sandbox-1", TEST_USER_ID))
                .thenReturn(Mono.empty());
        when(mcpServiceManager.stopMcpServer("sandbox-2", TEST_USER_ID))
                .thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Void> result = lifecycleManager.stopAllUserServers(TEST_USER_ID);

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();

        verify(mcpServiceManager).getUserMcpServers(TEST_USER_ID);
        verify(mcpServiceManager).stopMcpServer("sandbox-1", TEST_USER_ID);
        verify(mcpServiceManager).stopMcpServer("sandbox-2", TEST_USER_ID);
    }

    @Test
    void testStopAllUserServers_PartialFailure() {
        // 准备测试实例
        McpServerInstance instance1 = new McpServerInstance();
        instance1.setSandboxId("sandbox-1");
        instance1.setUserId(TEST_USER_ID);

        McpServerInstance instance2 = new McpServerInstance();
        instance2.setSandboxId("sandbox-2");
        instance2.setUserId(TEST_USER_ID);

        // 模拟服务调用
        when(mcpServiceManager.getUserMcpServers(TEST_USER_ID))
                .thenReturn(Flux.just(instance1, instance2));
        when(mcpServiceManager.stopMcpServer("sandbox-1", TEST_USER_ID))
                .thenReturn(Mono.empty());
        when(mcpServiceManager.stopMcpServer("sandbox-2", TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("停止失败")));

        // 调用被测试方法
        Mono<Void> result = lifecycleManager.stopAllUserServers(TEST_USER_ID);

        // 验证结果 - 即使部分失败也应该完成
        StepVerifier.create(result)
                .verifyComplete();

        verify(mcpServiceManager).getUserMcpServers(TEST_USER_ID);
        verify(mcpServiceManager).stopMcpServer("sandbox-1", TEST_USER_ID);
        verify(mcpServiceManager).stopMcpServer("sandbox-2", TEST_USER_ID);
    }

    @Test
    void testShutdownAllServers() {
        // 这个方法是同步的，测试其行为
        assertDoesNotThrow(() -> lifecycleManager.shutdownAllServers());
    }
}
