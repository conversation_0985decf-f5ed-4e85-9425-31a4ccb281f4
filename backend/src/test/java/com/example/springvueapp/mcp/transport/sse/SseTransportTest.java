package com.example.springvueapp.mcp.transport.sse;

import com.example.springvueapp.mcp.protocol.JsonRpcMessage;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.mcp.protocol.JsonRpcNotification;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SseTransport 的单元测试
 * 测试基于SSE的MCP传输实现
 */
@ExtendWith(MockitoExtension.class)
public class SseTransportTest {

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.RequestBodyUriSpec requestBodyUriSpec;

    @Mock
    private WebClient.RequestBodySpec requestBodySpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    private SseTransport sseTransport;
    private ObjectMapper objectMapper;

    private static final String TEST_SERVER_URL = "http://localhost:8080";
    private static final Map<String, String> TEST_HEADERS = Map.of("Authorization", "Bearer token");

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        sseTransport = new SseTransport(TEST_SERVER_URL, TEST_HEADERS);
        
        // 通过反射设置WebClient mock
        try {
            var webClientField = SseTransport.class.getDeclaredField("webClient");
            webClientField.setAccessible(true);
            webClientField.set(sseTransport, webClient);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject WebClient mock", e);
        }
    }

    @Test
    void testGetType() {
        assertEquals("sse", sseTransport.getType());
    }

    @Test
    void testConnect_WhenNotConnected() {
        // 模拟SSE连接
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.just("test-message"));

        // 调用被测试方法
        Mono<Void> result = sseTransport.connect();

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();

        assertTrue(sseTransport.isConnected());
    }

    @Test
    void testConnect_WhenAlreadyConnected() {
        // 先连接一次
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.just("test-message"));

        sseTransport.connect().block();

        // 再次连接应该立即返回
        Mono<Void> result = sseTransport.connect();

        StepVerifier.create(result)
                .verifyComplete();

        assertTrue(sseTransport.isConnected());
    }

    @Test
    void testDisconnect_WhenConnected() {
        // 先连接
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.just("test-message"));

        sseTransport.connect().block();
        assertTrue(sseTransport.isConnected());

        // 断开连接
        Mono<Void> result = sseTransport.disconnect();

        StepVerifier.create(result)
                .verifyComplete();

        assertFalse(sseTransport.isConnected());
    }

    @Test
    void testDisconnect_WhenNotConnected() {
        // 未连接时断开应该立即返回
        Mono<Void> result = sseTransport.disconnect();

        StepVerifier.create(result)
                .verifyComplete();

        assertFalse(sseTransport.isConnected());
    }

    @Test
    void testSendRequest_WhenConnected() {
        // 先连接
        setupConnection();

        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId("req-123");
        request.setMethod("test.method");

        // 模拟发送请求
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/mcp")).thenReturn(requestBodySpec);
        when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(Void.class)).thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Void> result = sseTransport.sendRequest(request);

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();

        verify(webClient).post();
    }

    @Test
    void testSendRequest_WhenNotConnected() {
        // 未连接时发送请求应该失败
        JsonRpcRequest request = new JsonRpcRequest();
        request.setMethod("test.method");

        Mono<Void> result = sseTransport.sendRequest(request);

        StepVerifier.create(result)
                .verifyError(IllegalStateException.class);
    }

    @Test
    void testSendResponse_WhenConnected() {
        // 先连接
        setupConnection();

        // 准备测试数据
        JsonRpcResponse response = new JsonRpcResponse();
        response.setId("resp-123");
        response.setResult(Map.of("success", true));

        // 模拟发送响应
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/mcp")).thenReturn(requestBodySpec);
        when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(Void.class)).thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Void> result = sseTransport.sendResponse(response);

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();

        verify(webClient).post();
    }

    @Test
    void testSendNotification_WhenConnected() {
        // 先连接
        setupConnection();

        // 准备测试数据
        JsonRpcNotification notification = new JsonRpcNotification();
        notification.setMethod("test.notification");
        notification.setParams(Map.of("data", "test"));

        // 模拟发送通知
        when(webClient.post()).thenReturn(requestBodyUriSpec);
        when(requestBodyUriSpec.uri("/mcp")).thenReturn(requestBodySpec);
        when(requestBodySpec.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpec);
        when(requestBodySpec.bodyValue(anyString())).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(Void.class)).thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Void> result = sseTransport.sendNotification(notification);

        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();

        verify(webClient).post();
    }

    @Test
    void testReceiveMessages() {
        // 测试消息接收流
        Flux<JsonRpcMessage> messageStream = sseTransport.receiveMessages();

        assertNotNull(messageStream);
        
        // 验证流是空的（因为没有连接）
        StepVerifier.create(messageStream.take(1))
                .expectTimeout(java.time.Duration.ofMillis(100))
                .verify();
    }

    @Test
    void testGetErrors() {
        // 测试错误流
        Flux<Throwable> errorStream = sseTransport.getErrors();

        assertNotNull(errorStream);
        
        // 验证流是空的（因为没有错误）
        StepVerifier.create(errorStream.take(1))
                .expectTimeout(java.time.Duration.ofMillis(100))
                .verify();
    }

    @Test
    void testGetConnectionStatus() {
        // 测试连接状态流
        Flux<Boolean> statusStream = sseTransport.getConnectionStatus();

        assertNotNull(statusStream);

        // 简单验证流不为空
        StepVerifier.create(statusStream.take(1))
                .expectTimeout(java.time.Duration.ofMillis(100))
                .verify();
    }

    @Test
    void testSendMessage_SerializationError() {
        // 先连接
        setupConnection();

        // 创建一个无法序列化的对象
        Object invalidMessage = new Object() {
            @SuppressWarnings("unused")
            public Object getSelf() {
                return this; // 循环引用，会导致序列化失败
            }
        };

        // 使用反射调用私有方法sendMessage
        try {
            var sendMessageMethod = SseTransport.class.getDeclaredMethod("sendMessage", Object.class);
            sendMessageMethod.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            Mono<Void> result = (Mono<Void>) sendMessageMethod.invoke(sseTransport, invalidMessage);

            StepVerifier.create(result)
                    .verifyError(RuntimeException.class);
        } catch (Exception e) {
            fail("Failed to test serialization error: " + e.getMessage());
        }
    }

    private void setupConnection() {
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri("/sse")).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.accept(MediaType.TEXT_EVENT_STREAM)).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToFlux(String.class)).thenReturn(Flux.just("test-message"));

        sseTransport.connect().block();
    }
}
