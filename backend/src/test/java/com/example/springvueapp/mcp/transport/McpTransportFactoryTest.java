package com.example.springvueapp.mcp.transport;

import com.example.springvueapp.mcp.transport.sse.SseTransport;
import com.example.springvueapp.mcp.transport.stdio.StdioTransport;
import com.example.springvueapp.sandbox.SandboxInstance;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * McpTransportFactory 的单元测试
 * 测试MCP传输工厂的创建功能
 */
@ExtendWith(MockitoExtension.class)
public class McpTransportFactoryTest {

    @Mock
    private SandboxInstance mockSandboxInstance;

    private McpTransportFactory factory;

    @BeforeEach
    void setUp() {
        factory = new McpTransportFactory();
    }

    @Test
    void testCreateStdioTransport() {
        // 调用被测试方法
        McpTransport transport = factory.createStdioTransport(mockSandboxInstance);

        // 验证结果
        assertNotNull(transport);
        assertInstanceOf(StdioTransport.class, transport);
        assertEquals("stdio", transport.getType());
    }

    @Test
    void testCreateSseTransport() {
        // 准备测试数据
        String serverUrl = "http://localhost:8080";
        Map<String, String> headers = Map.of("Authorization", "Bearer token");

        // 调用被测试方法
        McpTransport transport = factory.createSseTransport(serverUrl, headers);

        // 验证结果
        assertNotNull(transport);
        assertInstanceOf(SseTransport.class, transport);
        assertEquals("sse", transport.getType());
    }

    @Test
    void testCreateSseTransport_WithNullHeaders() {
        // 准备测试数据
        String serverUrl = "http://localhost:8080";

        // 调用被测试方法
        McpTransport transport = factory.createSseTransport(serverUrl, null);

        // 验证结果
        assertNotNull(transport);
        assertInstanceOf(SseTransport.class, transport);
        assertEquals("sse", transport.getType());
    }

    @Test
    void testCreateTransport_Stdio() {
        // 准备测试参数
        Map<String, Object> params = new HashMap<>();
        params.put("sandboxInstance", mockSandboxInstance);

        // 调用被测试方法
        McpTransport transport = factory.createTransport("stdio", params);

        // 验证结果
        assertNotNull(transport);
        assertInstanceOf(StdioTransport.class, transport);
        assertEquals("stdio", transport.getType());
    }

    @Test
    void testCreateTransport_Stdio_CaseInsensitive() {
        // 准备测试参数
        Map<String, Object> params = new HashMap<>();
        params.put("sandboxInstance", mockSandboxInstance);

        // 调用被测试方法 - 使用大写
        McpTransport transport = factory.createTransport("STDIO", params);

        // 验证结果
        assertNotNull(transport);
        assertInstanceOf(StdioTransport.class, transport);
        assertEquals("stdio", transport.getType());
    }

    @Test
    void testCreateTransport_Stdio_MissingSandboxInstance() {
        // 准备测试参数 - 缺少sandboxInstance
        Map<String, Object> params = new HashMap<>();

        // 调用被测试方法应该抛出异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> factory.createTransport("stdio", params)
        );

        assertEquals("stdio传输需要sandboxInstance参数", exception.getMessage());
    }

    @Test
    void testCreateTransport_Sse() {
        // 准备测试参数
        Map<String, Object> params = new HashMap<>();
        params.put("serverUrl", "http://localhost:8080");
        params.put("headers", Map.of("Authorization", "Bearer token"));

        // 调用被测试方法
        McpTransport transport = factory.createTransport("sse", params);

        // 验证结果
        assertNotNull(transport);
        assertInstanceOf(SseTransport.class, transport);
        assertEquals("sse", transport.getType());
    }

    @Test
    void testCreateTransport_Sse_CaseInsensitive() {
        // 准备测试参数
        Map<String, Object> params = new HashMap<>();
        params.put("serverUrl", "http://localhost:8080");

        // 调用被测试方法 - 使用混合大小写
        McpTransport transport = factory.createTransport("SSE", params);

        // 验证结果
        assertNotNull(transport);
        assertInstanceOf(SseTransport.class, transport);
        assertEquals("sse", transport.getType());
    }

    @Test
    void testCreateTransport_Sse_WithoutHeaders() {
        // 准备测试参数 - 没有headers
        Map<String, Object> params = new HashMap<>();
        params.put("serverUrl", "http://localhost:8080");

        // 调用被测试方法
        McpTransport transport = factory.createTransport("sse", params);

        // 验证结果
        assertNotNull(transport);
        assertInstanceOf(SseTransport.class, transport);
        assertEquals("sse", transport.getType());
    }

    @Test
    void testCreateTransport_Sse_MissingServerUrl() {
        // 准备测试参数 - 缺少serverUrl
        Map<String, Object> params = new HashMap<>();
        params.put("headers", Map.of("Authorization", "Bearer token"));

        // 调用被测试方法应该抛出异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> factory.createTransport("sse", params)
        );

        assertEquals("SSE传输需要serverUrl参数", exception.getMessage());
    }

    @Test
    void testCreateTransport_UnsupportedType() {
        // 准备测试参数
        Map<String, Object> params = new HashMap<>();

        // 调用被测试方法应该抛出异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> factory.createTransport("websocket", params)
        );

        assertEquals("不支持的传输类型: websocket", exception.getMessage());
    }

    @Test
    void testCreateTransport_NullType() {
        // 准备测试参数
        Map<String, Object> params = new HashMap<>();

        // 调用被测试方法应该抛出异常
        assertThrows(
                NullPointerException.class,
                () -> factory.createTransport(null, params)
        );
    }

    @Test
    void testCreateTransport_EmptyType() {
        // 准备测试参数
        Map<String, Object> params = new HashMap<>();

        // 调用被测试方法应该抛出异常
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> factory.createTransport("", params)
        );

        assertEquals("不支持的传输类型: ", exception.getMessage());
    }

    @Test
    void testCreateTransport_NullParams() {
        // 调用被测试方法应该抛出异常
        assertThrows(
                NullPointerException.class,
                () -> factory.createTransport("stdio", null)
        );
    }

    @Test
    void testCreateTransport_EmptyParams() {
        // 准备空的参数
        Map<String, Object> params = new HashMap<>();

        // stdio类型需要sandboxInstance参数
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> factory.createTransport("stdio", params)
        );

        assertEquals("stdio传输需要sandboxInstance参数", exception.getMessage());
    }

    @Test
    void testCreateTransport_InvalidParameterType() {
        // 准备错误类型的参数
        Map<String, Object> params = new HashMap<>();
        params.put("sandboxInstance", "not-a-sandbox-instance"); // 错误的类型

        // 调用被测试方法应该抛出异常
        assertThrows(
                ClassCastException.class,
                () -> factory.createTransport("stdio", params)
        );
    }

    @Test
    void testCreateTransport_Sse_InvalidHeadersType() {
        // 准备错误类型的headers参数
        Map<String, Object> params = new HashMap<>();
        params.put("serverUrl", "http://localhost:8080");
        params.put("headers", "not-a-map"); // 错误的类型

        // 调用被测试方法应该抛出异常
        assertThrows(
                ClassCastException.class,
                () -> factory.createTransport("sse", params)
        );
    }
}
