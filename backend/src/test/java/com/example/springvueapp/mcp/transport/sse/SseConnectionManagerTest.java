package com.example.springvueapp.mcp.transport.sse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SseConnectionManager 单元测试
 */
@ExtendWith(MockitoExtension.class)
class SseConnectionManagerTest {
    
    private SseConnectionManager connectionManager;
    
    private static final String TEST_SANDBOX_ID = "test-sandbox-123";
    private static final Long TEST_USER_ID = 1L;
    private static final String TEST_SANDBOX_ID_2 = "test-sandbox-456";
    private static final Long TEST_USER_ID_2 = 2L;
    
    @BeforeEach
    void setUp() {
        connectionManager = new SseConnectionManager();
    }
    
    @Test
    void testCreateConnection_Success() {
        // 执行测试
        StepVerifier.create(connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID))
                .expectNextMatches(transport -> 
                    transport != null && 
                    transport.getConnectionId().equals(TEST_SANDBOX_ID + ":" + TEST_USER_ID) &&
                    transport.isConnected())
                .verifyComplete();
        
        // 验证连接计数
        assertEquals(1, connectionManager.getActiveConnectionCount());
        assertEquals(1, connectionManager.getUserActiveConnectionCount(TEST_USER_ID));
        assertEquals(1, connectionManager.getSandboxActiveConnectionCount(TEST_SANDBOX_ID));
    }
    
    @Test
    void testCreateConnection_ReuseExisting() {
        // 创建第一个连接
        SseServerTransport transport1 = connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        assertNotNull(transport1);
        
        // 尝试创建相同的连接
        SseServerTransport transport2 = connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        assertNotNull(transport2);
        
        // 验证返回的是同一个连接
        assertEquals(transport1.getConnectionId(), transport2.getConnectionId());
        
        // 验证连接计数没有增加
        assertEquals(1, connectionManager.getActiveConnectionCount());
    }
    
    @Test
    void testCreateMultipleConnections() {
        // 创建多个不同的连接
        StepVerifier.create(connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID))
                .expectNextMatches(SseServerTransport::isConnected)
                .verifyComplete();
        
        StepVerifier.create(connectionManager.createConnection(TEST_SANDBOX_ID_2, TEST_USER_ID))
                .expectNextMatches(SseServerTransport::isConnected)
                .verifyComplete();
        
        StepVerifier.create(connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID_2))
                .expectNextMatches(SseServerTransport::isConnected)
                .verifyComplete();
        
        // 验证连接计数
        assertEquals(3, connectionManager.getActiveConnectionCount());
        assertEquals(2, connectionManager.getUserActiveConnectionCount(TEST_USER_ID));
        assertEquals(1, connectionManager.getUserActiveConnectionCount(TEST_USER_ID_2));
        assertEquals(2, connectionManager.getSandboxActiveConnectionCount(TEST_SANDBOX_ID));
        assertEquals(1, connectionManager.getSandboxActiveConnectionCount(TEST_SANDBOX_ID_2));
    }
    
    @Test
    void testGetConnection_Exists() {
        // 先创建连接
        SseServerTransport created = connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        assertNotNull(created);
        
        // 获取连接
        StepVerifier.create(connectionManager.getConnection(TEST_SANDBOX_ID, TEST_USER_ID))
                .expectNextMatches(transport -> 
                    transport.getConnectionId().equals(created.getConnectionId()))
                .verifyComplete();
    }
    
    @Test
    void testGetConnection_NotExists() {
        // 获取不存在的连接
        StepVerifier.create(connectionManager.getConnection(TEST_SANDBOX_ID, TEST_USER_ID))
                .verifyComplete(); // 应该返回空
    }
    
    @Test
    void testCloseConnection_Success() {
        // 先创建连接
        connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        assertEquals(1, connectionManager.getActiveConnectionCount());
        
        // 关闭连接
        StepVerifier.create(connectionManager.closeConnection(TEST_SANDBOX_ID, TEST_USER_ID))
                .verifyComplete();
        
        // 验证连接已关闭
        assertEquals(0, connectionManager.getActiveConnectionCount());
    }
    
    @Test
    void testCloseConnection_NotExists() {
        // 关闭不存在的连接
        StepVerifier.create(connectionManager.closeConnection(TEST_SANDBOX_ID, TEST_USER_ID))
                .verifyComplete(); // 应该正常完成，不抛出异常
        
        assertEquals(0, connectionManager.getActiveConnectionCount());
    }
    
    @Test
    void testCloseUserConnections_Success() {
        // 创建多个连接
        connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        connectionManager.createConnection(TEST_SANDBOX_ID_2, TEST_USER_ID).block();
        connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID_2).block();
        
        assertEquals(3, connectionManager.getActiveConnectionCount());
        assertEquals(2, connectionManager.getUserActiveConnectionCount(TEST_USER_ID));
        
        // 关闭用户1的所有连接
        StepVerifier.create(connectionManager.closeUserConnections(TEST_USER_ID))
                .verifyComplete();
        
        // 验证只剩下用户2的连接
        assertEquals(1, connectionManager.getActiveConnectionCount());
        assertEquals(0, connectionManager.getUserActiveConnectionCount(TEST_USER_ID));
        assertEquals(1, connectionManager.getUserActiveConnectionCount(TEST_USER_ID_2));
    }
    
    @Test
    void testHasAccess() {
        // 创建连接
        connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        
        // 验证访问权限
        assertTrue(connectionManager.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID));
        assertFalse(connectionManager.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID_2));
        assertFalse(connectionManager.hasAccess(TEST_SANDBOX_ID_2, TEST_USER_ID));
    }
    
    @Test
    void testGetConnectionStats() {
        // 创建一些连接
        connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        connectionManager.createConnection(TEST_SANDBOX_ID_2, TEST_USER_ID).block();
        
        // 获取统计信息
        Map<String, Object> stats = connectionManager.getConnectionStats();
        
        assertNotNull(stats);
        assertEquals(2, stats.get("totalConnections"));
        assertEquals(2, stats.get("activeConnections"));
        assertEquals(0, stats.get("inactiveConnections"));
        assertNotNull(stats.get("lastCleanup"));
    }
    
    @Test
    void testCloseAllConnections() {
        // 创建多个连接
        connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        connectionManager.createConnection(TEST_SANDBOX_ID_2, TEST_USER_ID).block();
        connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID_2).block();
        
        assertEquals(3, connectionManager.getActiveConnectionCount());
        
        // 关闭所有连接
        connectionManager.closeAllConnections();
        
        // 验证所有连接都已关闭
        assertEquals(0, connectionManager.getActiveConnectionCount());
        assertEquals(0, connectionManager.getUserActiveConnectionCount(TEST_USER_ID));
        assertEquals(0, connectionManager.getUserActiveConnectionCount(TEST_USER_ID_2));
    }
    
    @Test
    void testConnectionCounting() {
        // 测试各种连接计数方法
        assertEquals(0, connectionManager.getActiveConnectionCount());
        assertEquals(0, connectionManager.getUserActiveConnectionCount(TEST_USER_ID));
        assertEquals(0, connectionManager.getSandboxActiveConnectionCount(TEST_SANDBOX_ID));
        
        // 创建连接
        connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        connectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID_2).block();
        connectionManager.createConnection(TEST_SANDBOX_ID_2, TEST_USER_ID).block();
        
        // 验证计数
        assertEquals(3, connectionManager.getActiveConnectionCount());
        assertEquals(2, connectionManager.getUserActiveConnectionCount(TEST_USER_ID));
        assertEquals(1, connectionManager.getUserActiveConnectionCount(TEST_USER_ID_2));
        assertEquals(2, connectionManager.getSandboxActiveConnectionCount(TEST_SANDBOX_ID));
        assertEquals(1, connectionManager.getSandboxActiveConnectionCount(TEST_SANDBOX_ID_2));
        
        // 关闭一个连接
        connectionManager.closeConnection(TEST_SANDBOX_ID, TEST_USER_ID).block();
        
        // 重新验证计数
        assertEquals(2, connectionManager.getActiveConnectionCount());
        assertEquals(1, connectionManager.getUserActiveConnectionCount(TEST_USER_ID));
        assertEquals(1, connectionManager.getUserActiveConnectionCount(TEST_USER_ID_2));
        assertEquals(1, connectionManager.getSandboxActiveConnectionCount(TEST_SANDBOX_ID));
        assertEquals(1, connectionManager.getSandboxActiveConnectionCount(TEST_SANDBOX_ID_2));
    }
}
