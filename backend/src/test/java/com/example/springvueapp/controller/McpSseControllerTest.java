package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.service.McpServerLifecycleManager;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.model.McpServerStatus;
import com.example.springvueapp.service.McpSseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.core.Authentication;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * McpSseController 的单元测试
 * 测试所有SSE端点和错误处理
 */
@ExtendWith(MockitoExtension.class)
public class McpSseControllerTest {

    @Mock
    private McpSseService mcpSseService;

    @Mock
    private McpServerLifecycleManager lifecycleManager;

    @Mock
    private Authentication authentication;

    @InjectMocks
    private McpSseController mcpSseController;

    private static final String TEST_SANDBOX_ID = "test-sandbox-123";
    private static final Long TEST_USER_ID = 1L;
    private static final Long TEST_CONFIG_ID = 100L;

    @BeforeEach
    void setUp() {
        // 模拟认证信息返回用户ID
        when(authentication.getName()).thenReturn(TEST_USER_ID.toString());
    }

    @Test
    void testStreamMcpEvents_Success() {
        // 准备测试数据
        Flux<String> mockEventStream = Flux.just("event1", "event2", "event3");
        when(mcpSseService.streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(mockEventStream);

        // 调用被测试方法
        Flux<ServerSentEvent<String>> result = mcpSseController.streamMcpEvents(TEST_SANDBOX_ID, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(event -> {
                    assertEquals("mcp-message", event.event());
                    assertEquals("event1", event.data());
                    assertNotNull(event.id());
                })
                .assertNext(event -> {
                    assertEquals("mcp-message", event.event());
                    assertEquals("event2", event.data());
                    assertNotNull(event.id());
                })
                .assertNext(event -> {
                    assertEquals("mcp-message", event.event());
                    assertEquals("event3", event.data());
                    assertNotNull(event.id());
                })
                .verifyComplete();

        // 验证服务调用
        verify(mcpSseService).streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testStreamMcpEvents_ServiceError() {
        // 模拟服务抛出异常
        when(mcpSseService.streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Flux.error(new RuntimeException("连接失败")));

        // 调用被测试方法
        Flux<ServerSentEvent<String>> result = mcpSseController.streamMcpEvents(TEST_SANDBOX_ID, authentication);

        // 验证错误处理
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);

        verify(mcpSseService).streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testSendMcpRequest_Success() {
        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId("req-123");
        request.setMethod("test.method");
        request.setParams(Map.of("param1", "value1"));

        when(mcpSseService.sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request))
                .thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.sendMcpRequest(TEST_SANDBOX_ID, request, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(true, response.get("success"));
                    assertEquals("请求已发送", response.get("message"));
                    assertEquals("req-123", response.get("requestId"));
                })
                .verifyComplete();

        verify(mcpSseService).sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request);
    }

    @Test
    void testSendMcpRequest_WithNullId() {
        // 准备测试数据 - 请求ID为null
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId(null);
        request.setMethod("test.method");

        when(mcpSseService.sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request))
                .thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.sendMcpRequest(TEST_SANDBOX_ID, request, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(true, response.get("success"));
                    assertEquals("请求已发送", response.get("message"));
                    assertEquals("unknown", response.get("requestId"));
                })
                .verifyComplete();
    }

    @Test
    void testSendMcpRequest_ServiceError() {
        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setMethod("test.method");

        when(mcpSseService.sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request))
                .thenReturn(Mono.error(new RuntimeException("发送失败")));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.sendMcpRequest(TEST_SANDBOX_ID, request, authentication);

        // 验证错误处理
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(false, response.get("success"));
                    assertEquals("发送失败", response.get("error"));
                })
                .verifyComplete();
    }

    @Test
    void testGetMcpServerStatus_Success() {
        // 准备测试数据
        McpServerStatus mockStatus = new McpServerStatus(TEST_SANDBOX_ID, true);
        mockStatus.setLastActivity(LocalDateTime.now());
        mockStatus.setActiveConnections(2);

        when(mcpSseService.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(mockStatus));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.getMcpServerStatus(TEST_SANDBOX_ID, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(TEST_SANDBOX_ID, response.get("sandboxId"));
                    assertEquals(true, response.get("connected"));
                    assertNotNull(response.get("lastActivity"));
                    assertEquals(2, response.get("activeConnections"));
                })
                .verifyComplete();

        verify(mcpSseService).getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testGetMcpServerStatus_ServiceError() {
        // 模拟服务抛出异常
        when(mcpSseService.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("状态获取失败")));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.getMcpServerStatus(TEST_SANDBOX_ID, authentication);

        // 验证错误处理
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(TEST_SANDBOX_ID, response.get("sandboxId"));
                    assertEquals(false, response.get("connected"));
                    assertEquals("状态获取失败", response.get("error"));
                })
                .verifyComplete();
    }

    @Test
    void testPingMcpServer_Success() {
        // 准备测试数据
        Long mockLatency = 150L;
        when(mcpSseService.pingMcpServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(mockLatency));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.pingMcpServer(TEST_SANDBOX_ID, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(true, response.get("success"));
                    assertEquals(mockLatency, response.get("latency"));
                    assertNotNull(response.get("timestamp"));
                })
                .verifyComplete();

        verify(mcpSseService).pingMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testPingMcpServer_ServiceError() {
        // 模拟服务抛出异常
        when(mcpSseService.pingMcpServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("Ping失败")));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.pingMcpServer(TEST_SANDBOX_ID, authentication);

        // 验证错误处理
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(false, response.get("success"));
                    assertEquals("Ping失败", response.get("error"));
                    assertNotNull(response.get("timestamp"));
                })
                .verifyComplete();
    }

    @Test
    void testStartMcpServer_Success() {
        // 准备测试数据
        McpServerInstance mockInstance = new McpServerInstance();
        mockInstance.setSandboxId(TEST_SANDBOX_ID);
        mockInstance.setConfigurationId(TEST_CONFIG_ID);

        when(lifecycleManager.startServer(TEST_CONFIG_ID, TEST_USER_ID))
                .thenReturn(Mono.just(mockInstance));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.startMcpServer(TEST_CONFIG_ID, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(true, response.get("success"));
                    assertEquals("服务器启动成功", response.get("message"));
                    assertEquals(mockInstance, response.get("instance"));
                })
                .verifyComplete();

        verify(lifecycleManager).startServer(TEST_CONFIG_ID, TEST_USER_ID);
    }

    @Test
    void testStartMcpServer_ServiceError() {
        // 模拟服务抛出异常
        when(lifecycleManager.startServer(TEST_CONFIG_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("启动失败")));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.startMcpServer(TEST_CONFIG_ID, authentication);

        // 验证错误处理
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(false, response.get("success"));
                    assertEquals("启动失败", response.get("error"));
                })
                .verifyComplete();
    }

    @Test
    void testStopMcpServer_Success() {
        // 模拟成功停止
        when(lifecycleManager.stopServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.stopMcpServer(TEST_SANDBOX_ID, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(true, response.get("success"));
                    assertEquals("服务器停止成功", response.get("message"));
                    assertEquals(TEST_SANDBOX_ID, response.get("sandboxId"));
                })
                .verifyComplete();

        verify(lifecycleManager).stopServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testStopMcpServer_ServiceError() {
        // 模拟服务抛出异常
        when(lifecycleManager.stopServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("停止失败")));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.stopMcpServer(TEST_SANDBOX_ID, authentication);

        // 验证错误处理
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(false, response.get("success"));
                    assertEquals("停止失败", response.get("error"));
                })
                .verifyComplete();
    }

    @Test
    void testRestartMcpServer_Success() {
        // 准备测试数据
        McpServerInstance mockInstance = new McpServerInstance();
        mockInstance.setSandboxId(TEST_SANDBOX_ID);
        mockInstance.setConfigurationId(TEST_CONFIG_ID);

        when(lifecycleManager.restartServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(mockInstance));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.restartMcpServer(TEST_SANDBOX_ID, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(true, response.get("success"));
                    assertEquals("服务器重启成功", response.get("message"));
                    assertEquals(mockInstance, response.get("instance"));
                })
                .verifyComplete();

        verify(lifecycleManager).restartServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }

    @Test
    void testRestartMcpServer_ServiceError() {
        // 模拟服务抛出异常
        when(lifecycleManager.restartServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.error(new RuntimeException("重启失败")));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.restartMcpServer(TEST_SANDBOX_ID, authentication);

        // 验证错误处理
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(false, response.get("success"));
                    assertEquals("重启失败", response.get("error"));
                })
                .verifyComplete();
    }

    @Test
    void testGetUserMcpServers_Success() {
        // 准备测试数据
        McpServerInstance instance1 = new McpServerInstance();
        instance1.setSandboxId("sandbox-1");
        instance1.setConfigurationId(1L);

        McpServerInstance instance2 = new McpServerInstance();
        instance2.setSandboxId("sandbox-2");
        instance2.setConfigurationId(2L);

        when(lifecycleManager.getUserServers(TEST_USER_ID))
                .thenReturn(Flux.just(instance1, instance2));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.getUserMcpServers(authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(true, response.get("success"));
                    assertEquals(2, response.get("count"));
                    assertNotNull(response.get("servers"));
                })
                .verifyComplete();

        verify(lifecycleManager).getUserServers(TEST_USER_ID);
    }

    @Test
    void testGetUserMcpServers_ServiceError() {
        // 模拟服务抛出异常
        when(lifecycleManager.getUserServers(TEST_USER_ID))
                .thenReturn(Flux.error(new RuntimeException("获取服务器列表失败")));

        // 调用被测试方法
        Mono<Map<String, Object>> result = mcpSseController.getUserMcpServers(authentication);

        // 验证错误处理
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(false, response.get("success"));
                    assertEquals("获取服务器列表失败", response.get("error"));
                })
                .verifyComplete();
    }

    @Test
    void testGetUserId_WithValidNumericUsername() {
        // 测试用户名为数字的情况
        when(authentication.getName()).thenReturn("123");

        // 通过实际调用一个公共方法来间接测试getUserId方法
        when(mcpSseService.getMcpServerStatus(TEST_SANDBOX_ID, 123L))
                .thenReturn(Mono.just(new McpServerStatus(TEST_SANDBOX_ID, true)));

        Mono<Map<String, Object>> result = mcpSseController.getMcpServerStatus(TEST_SANDBOX_ID, authentication);

        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(TEST_SANDBOX_ID, response.get("sandboxId"));
                })
                .verifyComplete();

        verify(mcpSseService).getMcpServerStatus(TEST_SANDBOX_ID, 123L);
    }

    @Test
    void testGetUserId_WithInvalidUsername() {
        // 测试用户名不是数字的情况
        when(authentication.getName()).thenReturn("invalid-user");

        // 应该回退到默认用户ID 1L
        when(mcpSseService.getMcpServerStatus(TEST_SANDBOX_ID, 1L))
                .thenReturn(Mono.just(new McpServerStatus(TEST_SANDBOX_ID, true)));

        Mono<Map<String, Object>> result = mcpSseController.getMcpServerStatus(TEST_SANDBOX_ID, authentication);

        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(TEST_SANDBOX_ID, response.get("sandboxId"));
                })
                .verifyComplete();

        verify(mcpSseService).getMcpServerStatus(TEST_SANDBOX_ID, 1L);
    }
}
