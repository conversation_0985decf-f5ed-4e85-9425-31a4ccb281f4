package com.example.springvueapp.service;

import com.example.springvueapp.mcp.service.McpServiceManager;
import com.example.springvueapp.mcp.transport.sse.SseConnectionManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * McpSseService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class McpSseServiceTest {

    @Mock
    private SseConnectionManager connectionManager;

    @Mock
    private McpServiceManager serviceManager;

    @Mock
    private ObjectMapper objectMapper;

    private McpSseService mcpSseService;

    @BeforeEach
    void setUp() {
        mcpSseService = new McpSseService(serviceManager, connectionManager, objectMapper);
    }

    @Test
    void testServiceCreation() {
        // 测试服务能够正确创建
        assertNotNull(mcpSseService);
        assertNotNull(connectionManager);
        assertNotNull(serviceManager);
    }

    @Test
    void testServiceDependencies() {
        // 测试依赖注入正确
        assertNotNull(mcpSseService);
    }


}
