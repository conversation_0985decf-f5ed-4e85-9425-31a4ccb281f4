package com.example.springvueapp.repository;

import com.example.springvueapp.entity.UserEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.data.r2dbc.DataR2dbcTest;
import org.springframework.test.context.ActiveProfiles;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

/**
 * UserRepository 的单元测试
 * 使用 @DataR2dbcTest 注解来测试 R2DBC 仓库
 */
@DataR2dbcTest
@ActiveProfiles("test")
public class UserRepositoryTest {

    @Autowired
    private UserRepository userRepository;

    @Test
    public void testSaveUser() {
        // 创建测试用户
        UserEntity user = UserEntity.builder()
                .username("testuser")
                .password("password")
                .email("<EMAIL>")
                .fullName("Test User")
                .enabled(true)
                .build();

        // 保存用户并验证结果
        Mono<UserEntity> savedUserMono = userRepository.save(user);

        StepVerifier.create(savedUserMono)
                .expectNextMatches(savedUser -> 
                    savedUser.getId() != null &&
                    savedUser.getUsername().equals("testuser") &&
                    savedUser.getEmail().equals("<EMAIL>")
                )
                .verifyComplete();
    }

    @Test
    public void testFindByUsername() {
        // 创建测试用户
        UserEntity user = UserEntity.builder()
                .username("finduser")
                .password("password")
                .email("<EMAIL>")
                .fullName("Find User")
                .enabled(true)
                .build();

        // 保存用户
        userRepository.save(user).block();

        // 查找用户并验证结果
        Mono<UserEntity> foundUserMono = userRepository.findByUsername("finduser");

        StepVerifier.create(foundUserMono)
                .expectNextMatches(foundUser -> 
                    foundUser.getUsername().equals("finduser") &&
                    foundUser.getEmail().equals("<EMAIL>")
                )
                .verifyComplete();
    }

    @Test
    public void testExistsByUsername() {
        // 创建测试用户
        UserEntity user = UserEntity.builder()
                .username("existsuser")
                .password("password")
                .email("<EMAIL>")
                .fullName("Exists User")
                .enabled(true)
                .build();

        // 保存用户
        userRepository.save(user).block();

        // 检查用户是否存在
        Mono<Boolean> existsMono = userRepository.existsByUsername("existsuser");

        StepVerifier.create(existsMono)
                .expectNext(true)
                .verifyComplete();

        // 检查不存在的用户
        Mono<Boolean> notExistsMono = userRepository.existsByUsername("nonexistentuser");

        StepVerifier.create(notExistsMono)
                .expectNext(false)
                .verifyComplete();
    }
}
