package com.example.springvueapp.mcp.routing;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MCP路由解析器
 * 解析和验证MCP相关的URL路由
 */
@Component
public class McpRouteResolver {
    
    private static final Logger log = LoggerFactory.getLogger(McpRouteResolver.class);
    
    // URL路由模式
    private static final Pattern SSE_EVENTS_PATTERN = Pattern.compile("^/api/mcp/sse/([^/]+)/events$");
    private static final Pattern SSE_STATUS_PATTERN = Pattern.compile("^/api/mcp/sse/([^/]+)/status$");
    private static final Pattern SSE_PING_PATTERN = Pattern.compile("^/api/mcp/sse/([^/]+)/ping$");
    private static final Pattern SSE_SEND_PATTERN = Pattern.compile("^/api/mcp/sse/([^/]+)/send$");
    
    private static final Pattern SERVER_START_PATTERN = Pattern.compile("^/api/mcp/servers/([^/]+)/start$");
    private static final Pattern SERVER_STOP_PATTERN = Pattern.compile("^/api/mcp/servers/([^/]+)/stop$");
    private static final Pattern SERVER_RESTART_PATTERN = Pattern.compile("^/api/mcp/servers/([^/]+)/restart$");
    private static final Pattern SERVER_LIST_PATTERN = Pattern.compile("^/api/mcp/servers$");
    
    private static final Pattern HEALTH_PATTERN = Pattern.compile("^/api/mcp/health$");
    
    /**
     * 路由类型枚举
     */
    public enum RouteType {
        SSE_EVENTS,
        SSE_STATUS,
        SSE_PING,
        SSE_SEND,
        SERVER_START,
        SERVER_STOP,
        SERVER_RESTART,
        SERVER_LIST,
        HEALTH,
        UNKNOWN
    }
    
    /**
     * 路由信息类
     */
    public static class RouteInfo {
        private final RouteType type;
        private final String sandboxId;
        private final String configId;
        private final Map<String, String> parameters;
        
        public RouteInfo(RouteType type, String sandboxId, String configId, Map<String, String> parameters) {
            this.type = type;
            this.sandboxId = sandboxId;
            this.configId = configId;
            this.parameters = parameters;
        }
        
        public RouteType getType() {
            return type;
        }
        
        public String getSandboxId() {
            return sandboxId;
        }
        
        public String getConfigId() {
            return configId;
        }
        
        public Map<String, String> getParameters() {
            return parameters;
        }
        
        public boolean isValid() {
            return type != RouteType.UNKNOWN;
        }
        
        @Override
        public String toString() {
            return "RouteInfo{" +
                    "type=" + type +
                    ", sandboxId='" + sandboxId + '\'' +
                    ", configId='" + configId + '\'' +
                    ", parameters=" + parameters +
                    '}';
        }
    }
    
    /**
     * 解析URL路径并返回路由信息
     * 
     * @param path URL路径
     * @return 路由信息
     */
    public RouteInfo resolveRoute(String path) {
        if (path == null || path.trim().isEmpty()) {
            return new RouteInfo(RouteType.UNKNOWN, null, null, Map.of());
        }
        
        log.debug("解析路由: {}", path);
        
        // SSE相关路由
        Matcher matcher = SSE_EVENTS_PATTERN.matcher(path);
        if (matcher.matches()) {
            String sandboxId = matcher.group(1);
            return new RouteInfo(RouteType.SSE_EVENTS, sandboxId, null, Map.of("sandboxId", sandboxId));
        }
        
        matcher = SSE_STATUS_PATTERN.matcher(path);
        if (matcher.matches()) {
            String sandboxId = matcher.group(1);
            return new RouteInfo(RouteType.SSE_STATUS, sandboxId, null, Map.of("sandboxId", sandboxId));
        }
        
        matcher = SSE_PING_PATTERN.matcher(path);
        if (matcher.matches()) {
            String sandboxId = matcher.group(1);
            return new RouteInfo(RouteType.SSE_PING, sandboxId, null, Map.of("sandboxId", sandboxId));
        }
        
        matcher = SSE_SEND_PATTERN.matcher(path);
        if (matcher.matches()) {
            String sandboxId = matcher.group(1);
            return new RouteInfo(RouteType.SSE_SEND, sandboxId, null, Map.of("sandboxId", sandboxId));
        }
        
        // 服务器管理相关路由
        matcher = SERVER_START_PATTERN.matcher(path);
        if (matcher.matches()) {
            String configId = matcher.group(1);
            return new RouteInfo(RouteType.SERVER_START, null, configId, Map.of("configId", configId));
        }
        
        matcher = SERVER_STOP_PATTERN.matcher(path);
        if (matcher.matches()) {
            String sandboxId = matcher.group(1);
            return new RouteInfo(RouteType.SERVER_STOP, sandboxId, null, Map.of("sandboxId", sandboxId));
        }
        
        matcher = SERVER_RESTART_PATTERN.matcher(path);
        if (matcher.matches()) {
            String sandboxId = matcher.group(1);
            return new RouteInfo(RouteType.SERVER_RESTART, sandboxId, null, Map.of("sandboxId", sandboxId));
        }
        
        // 服务器列表路由
        if (SERVER_LIST_PATTERN.matcher(path).matches()) {
            return new RouteInfo(RouteType.SERVER_LIST, null, null, Map.of());
        }
        
        // 健康检查路由
        if (HEALTH_PATTERN.matcher(path).matches()) {
            return new RouteInfo(RouteType.HEALTH, null, null, Map.of());
        }
        
        log.debug("未识别的路由: {}", path);
        return new RouteInfo(RouteType.UNKNOWN, null, null, Map.of());
    }
    
    /**
     * 验证沙箱ID格式
     * 
     * @param sandboxId 沙箱ID
     * @return 是否有效
     */
    public boolean isValidSandboxId(String sandboxId) {
        if (sandboxId == null || sandboxId.trim().isEmpty()) {
            return false;
        }
        
        // 沙箱ID应该是字母数字组合，长度在8-64之间
        return sandboxId.matches("^[a-zA-Z0-9_-]{8,64}$");
    }
    
    /**
     * 验证配置ID格式
     * 
     * @param configId 配置ID
     * @return 是否有效
     */
    public boolean isValidConfigId(String configId) {
        if (configId == null || configId.trim().isEmpty()) {
            return false;
        }
        
        try {
            Long.parseLong(configId);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 构建SSE事件流URL
     * 
     * @param sandboxId 沙箱ID
     * @return URL路径
     */
    public String buildSseEventsUrl(String sandboxId) {
        if (!isValidSandboxId(sandboxId)) {
            throw new IllegalArgumentException("无效的沙箱ID: " + sandboxId);
        }
        return "/api/mcp/sse/" + sandboxId + "/events";
    }
    
    /**
     * 构建SSE状态查询URL
     * 
     * @param sandboxId 沙箱ID
     * @return URL路径
     */
    public String buildSseStatusUrl(String sandboxId) {
        if (!isValidSandboxId(sandboxId)) {
            throw new IllegalArgumentException("无效的沙箱ID: " + sandboxId);
        }
        return "/api/mcp/sse/" + sandboxId + "/status";
    }
    
    /**
     * 构建SSE心跳检测URL
     * 
     * @param sandboxId 沙箱ID
     * @return URL路径
     */
    public String buildSsePingUrl(String sandboxId) {
        if (!isValidSandboxId(sandboxId)) {
            throw new IllegalArgumentException("无效的沙箱ID: " + sandboxId);
        }
        return "/api/mcp/sse/" + sandboxId + "/ping";
    }
    
    /**
     * 构建SSE消息发送URL
     * 
     * @param sandboxId 沙箱ID
     * @return URL路径
     */
    public String buildSseSendUrl(String sandboxId) {
        if (!isValidSandboxId(sandboxId)) {
            throw new IllegalArgumentException("无效的沙箱ID: " + sandboxId);
        }
        return "/api/mcp/sse/" + sandboxId + "/send";
    }
    
    /**
     * 构建服务器启动URL
     * 
     * @param configId 配置ID
     * @return URL路径
     */
    public String buildServerStartUrl(String configId) {
        if (!isValidConfigId(configId)) {
            throw new IllegalArgumentException("无效的配置ID: " + configId);
        }
        return "/api/mcp/servers/" + configId + "/start";
    }
    
    /**
     * 构建服务器停止URL
     * 
     * @param sandboxId 沙箱ID
     * @return URL路径
     */
    public String buildServerStopUrl(String sandboxId) {
        if (!isValidSandboxId(sandboxId)) {
            throw new IllegalArgumentException("无效的沙箱ID: " + sandboxId);
        }
        return "/api/mcp/servers/" + sandboxId + "/stop";
    }
    
    /**
     * 构建服务器重启URL
     * 
     * @param sandboxId 沙箱ID
     * @return URL路径
     */
    public String buildServerRestartUrl(String sandboxId) {
        if (!isValidSandboxId(sandboxId)) {
            throw new IllegalArgumentException("无效的沙箱ID: " + sandboxId);
        }
        return "/api/mcp/servers/" + sandboxId + "/restart";
    }
    
    /**
     * 获取所有支持的路由模式
     * 
     * @return 路由模式列表
     */
    public Map<RouteType, String> getSupportedRoutes() {
        return Map.of(
            RouteType.SSE_EVENTS, "/api/mcp/sse/{sandboxId}/events",
            RouteType.SSE_STATUS, "/api/mcp/sse/{sandboxId}/status",
            RouteType.SSE_PING, "/api/mcp/sse/{sandboxId}/ping",
            RouteType.SSE_SEND, "/api/mcp/sse/{sandboxId}/send",
            RouteType.SERVER_START, "/api/mcp/servers/{configId}/start",
            RouteType.SERVER_STOP, "/api/mcp/servers/{sandboxId}/stop",
            RouteType.SERVER_RESTART, "/api/mcp/servers/{sandboxId}/restart",
            RouteType.SERVER_LIST, "/api/mcp/servers",
            RouteType.HEALTH, "/api/mcp/health"
        );
    }
}
