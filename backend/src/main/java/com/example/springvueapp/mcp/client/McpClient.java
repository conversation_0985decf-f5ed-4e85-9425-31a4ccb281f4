package com.example.springvueapp.mcp.client;

import com.example.springvueapp.mcp.model.*;
import com.example.springvueapp.mcp.protocol.JsonRpcNotification;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.sandbox.SandboxInstance;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 通过 stdio 与 MCP 服务器通信的 MCP 客户端
 */
public class McpClient {

    private static final Logger log = LoggerFactory.getLogger(McpClient.class);

    private final SandboxInstance sandboxInstance;
    private final ObjectMapper objectMapper;
    private final AtomicLong requestIdCounter;
    private final Map<Object, Sinks.One<JsonRpcResponse>> pendingRequests;
    private final Sinks.Many<JsonRpcNotification> notificationSink;

    private boolean initialized = false;
    private McpInitializeResponse initializeResponse;

    public McpClient(SandboxInstance sandboxInstance) {
        this.sandboxInstance = sandboxInstance;
        this.objectMapper = new ObjectMapper();
        this.requestIdCounter = new AtomicLong(1);
        this.pendingRequests = new ConcurrentHashMap<>();
        this.notificationSink = Sinks.many().multicast().onBackpressureBuffer();

        // Start listening to stdout for responses
        startResponseListener();
    }

    /**
     * Initialize the MCP session
     */
    public Mono<McpInitializeResponse> initialize() {
        if (initialized) {
            return Mono.just(initializeResponse);
        }

        McpInitializeRequest.McpClientInfo clientInfo = new McpInitializeRequest.McpClientInfo(
                "Spring MCP Proxy", "1.0.0");

        McpInitializeRequest initRequest = new McpInitializeRequest(
                "2024-11-05",
                Map.of("tools", Map.of("listChanged", true)),
                clientInfo
        );

        return sendRequest("initialize", initRequest, McpInitializeResponse.class)
                .doOnNext(response -> {
                    this.initializeResponse = response;
                    this.initialized = true;
                    log.info("MCP session initialized with server: {}",
                            response.getServerInfo() != null ? response.getServerInfo().getName() : "Unknown");
                });
    }

    /**
     * List available tools from the MCP server
     */
    public Mono<List<McpTool>> listTools() {
        return ensureInitialized()
                .then(sendRequest("tools/list", null, McpToolListResponse.class))
                .map(McpToolListResponse::getTools);
    }

    /**
     * Call a tool on the MCP server
     */
    public Mono<McpToolCallResponse> callTool(String toolName, Map<String, Object> arguments) {
        McpToolCallRequest request = new McpToolCallRequest(toolName, arguments);

        return ensureInitialized()
                .then(sendRequest("tools/call", request, McpToolCallResponse.class));
    }

    /**
     * Get server information
     */
    public Mono<McpServerInfo> getServerInfo() {
        return ensureInitialized()
                .map(response -> response.getServerInfo());
    }

    /**
     * Get notifications stream
     */
    public Flux<JsonRpcNotification> getNotifications() {
        return notificationSink.asFlux();
    }

    /**
     * 连接到MCP服务器
     */
    public Mono<Void> connect() {
        return Mono.fromRunnable(() -> {
            log.info("连接到MCP服务器");
            // startResponseListener已经在构造函数中调用了
        })
        .then(initialize())
        .then();
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return sandboxInstance.isRunning() && initialized;
    }

    /**
     * 发送JSON-RPC请求
     */
    public Mono<JsonRpcResponse> sendRequest(JsonRpcRequest request) {
        Sinks.One<JsonRpcResponse> responseSink = Sinks.one();
        pendingRequests.put(request.getId(), responseSink);

        return writeMessage(request)
                .then(responseSink.asMono())
                .timeout(Duration.ofSeconds(30))
                .doFinally(signal -> pendingRequests.remove(request.getId()));
    }

    /**
     * 接收消息流
     */
    public Flux<JsonRpcNotification> receiveMessages() {
        return notificationSink.asFlux();
    }

    /**
     * 心跳检测
     */
    public Mono<String> ping() {
        return ensureInitialized()
                .then(sendRequest("ping", null, String.class))
                .onErrorReturn("pong"); // 如果服务器不支持ping，返回默认响应
    }

    /**
     * Close the MCP client
     */
    public Mono<Void> close() {
        return Mono.fromRunnable(() -> {
            // Cancel all pending requests
            pendingRequests.values().forEach(sink ->
                sink.tryEmitError(new RuntimeException("Client closed")));
            pendingRequests.clear();

            // Complete notification sink
            notificationSink.tryEmitComplete();

            log.info("MCP client closed");
        });
    }

    private Mono<McpInitializeResponse> ensureInitialized() {
        if (initialized) {
            return Mono.just(initializeResponse);
        }
        return initialize();
    }

    private <T> Mono<T> sendRequest(String method, Object params, Class<T> responseType) {
        Object requestId = requestIdCounter.getAndIncrement();
        JsonRpcRequest request = new JsonRpcRequest(requestId, method, params);

        Sinks.One<JsonRpcResponse> responseSink = Sinks.one();
        pendingRequests.put(requestId, responseSink);

        return writeMessage(request)
                .then(responseSink.asMono())
                .timeout(Duration.ofSeconds(30))
                .map(response -> {
                    if (response.getError() != null) {
                        throw new McpException("MCP Error: " + response.getError().getMessage(),
                                response.getError());
                    }

                    try {
                        return objectMapper.convertValue(response.getResult(), responseType);
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to parse response", e);
                    }
                })
                .doFinally(signal -> pendingRequests.remove(requestId));
    }

    private Mono<Void> writeMessage(Object message) {
        return Mono.fromRunnable(() -> {
            try {
                String json = objectMapper.writeValueAsString(message);
                String messageWithNewline = json + "\n";

                log.debug("Sending MCP message: {}", json);
                sandboxInstance.writeToStdin(messageWithNewline).block();

            } catch (JsonProcessingException e) {
                throw new RuntimeException("Failed to serialize message", e);
            }
        });
    }

    private void startResponseListener() {
        sandboxInstance.getStdoutFlux()
                .filter(line -> !line.trim().isEmpty())
                .subscribe(
                        this::handleResponseLine,
                        error -> log.error("Error in response listener", error),
                        () -> log.info("Response listener completed")
                );
        sandboxInstance.getStderrFlux()
                .filter(line -> !line.trim().isEmpty())
                .subscribe(
                        log::warn,
                        error -> log.error("Error in response listener", error),
                        () -> log.info("Error listener completed")
                );
    }

    private void handleResponseLine(String line) {
        try {
            log.debug("Received MCP message: {}", line);

            // Try to parse as response first
            if (line.contains("\"id\"")) {
                JsonRpcResponse response = objectMapper.readValue(line, JsonRpcResponse.class);
                Sinks.One<JsonRpcResponse> sink = pendingRequests.get(response.getId());
                if (sink != null) {
                    sink.tryEmitValue(response);
                    return;
                }
            }

            // Try to parse as notification
            if (line.contains("\"method\"") && !line.contains("\"id\"")) {
                JsonRpcNotification notification = objectMapper.readValue(line, JsonRpcNotification.class);
                notificationSink.tryEmitNext(notification);
                return;
            }

            log.warn("Received unhandled message: {}", line);

        } catch (Exception e) {
            log.error("Failed to parse MCP message: {}", line, e);
        }
    }
}
