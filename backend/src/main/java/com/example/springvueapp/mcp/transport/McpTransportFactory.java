package com.example.springvueapp.mcp.transport;

import com.example.springvueapp.mcp.transport.sse.SseTransport;
import com.example.springvueapp.mcp.transport.stdio.StdioTransport;
import com.example.springvueapp.sandbox.SandboxInstance;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * MCP 传输工厂类
 * 根据配置创建不同类型的传输实现
 */
@Component
public class McpTransportFactory {
    
    /**
     * 创建stdio传输
     * @param sandboxInstance 沙箱实例
     * @return stdio传输实现
     */
    public McpTransport createStdioTransport(SandboxInstance sandboxInstance) {
        return new StdioTransport(sandboxInstance);
    }
    
    /**
     * 创建SSE传输
     * @param serverUrl SSE服务器URL
     * @param headers 请求头
     * @return SSE传输实现
     */
    public McpTransport createSseTransport(String serverUrl, Map<String, String> headers) {
        return new SseTransport(serverUrl, headers);
    }
    
    /**
     * 根据类型和参数创建传输
     * @param type 传输类型
     * @param params 参数
     * @return 传输实现
     */
    public McpTransport createTransport(String type, Map<String, Object> params) {
        switch (type.toLowerCase()) {
            case "stdio":
                SandboxInstance sandboxInstance = (SandboxInstance) params.get("sandboxInstance");
                if (sandboxInstance == null) {
                    throw new IllegalArgumentException("stdio传输需要sandboxInstance参数");
                }
                return createStdioTransport(sandboxInstance);
                
            case "sse":
                String serverUrl = (String) params.get("serverUrl");
                if (serverUrl == null) {
                    throw new IllegalArgumentException("SSE传输需要serverUrl参数");
                }
                @SuppressWarnings("unchecked")
                Map<String, String> headers = (Map<String, String>) params.get("headers");
                return createSseTransport(serverUrl, headers);
                
            default:
                throw new IllegalArgumentException("不支持的传输类型: " + type);
        }
    }
}
