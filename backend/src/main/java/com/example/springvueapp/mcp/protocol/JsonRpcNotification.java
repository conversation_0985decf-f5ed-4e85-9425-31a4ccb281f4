package com.example.springvueapp.mcp.protocol;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * JSON-RPC 2.0 通知消息（无id字段）
 */
public class JsonRpcNotification extends JsonRpcMessage {

    @JsonProperty("method")
    private String method;

    @JsonProperty("params")
    private Object params;

    public JsonRpcNotification() {
        super();
    }

    public JsonRpcNotification(String method, Object params) {
        super();
        this.method = method;
        this.params = params;
    }

    public JsonRpcNotification(String jsonrpc, String method, Object params) {
        super(jsonrpc, null); // 通知消息没有id
        this.method = method;
        this.params = params;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Object getParams() {
        return params;
    }

    public void setParams(Object params) {
        this.params = params;
    }
}
