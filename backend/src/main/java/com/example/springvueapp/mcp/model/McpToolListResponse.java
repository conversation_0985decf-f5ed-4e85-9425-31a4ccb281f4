package com.example.springvueapp.mcp.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * MCP 工具列表响应
 */
public class McpToolListResponse {
    
    @JsonProperty("tools")
    private List<McpTool> tools;

    public McpToolListResponse() {
    }

    public McpToolListResponse(List<McpTool> tools) {
        this.tools = tools;
    }

    public List<McpTool> getTools() {
        return tools;
    }

    public void setTools(List<McpTool> tools) {
        this.tools = tools;
    }
}
