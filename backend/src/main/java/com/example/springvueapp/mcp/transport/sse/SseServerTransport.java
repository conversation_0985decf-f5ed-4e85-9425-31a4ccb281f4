package com.example.springvueapp.mcp.transport.sse;

import com.example.springvueapp.mcp.protocol.JsonRpcMessage;
import com.example.springvueapp.mcp.protocol.JsonRpcNotification;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.mcp.transport.McpTransport;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 服务器端SSE传输实现
 * 用于向客户端推送MCP消息
 */
public class SseServerTransport implements McpTransport {
    
    private static final Logger log = LoggerFactory.getLogger(SseServerTransport.class);
    
    private final String connectionId;
    private final ObjectMapper objectMapper;
    
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final Sinks.Many<JsonRpcMessage> messageSink;
    private final Sinks.Many<Throwable> errorSink;
    private final Sinks.Many<Boolean> connectionStatusSink;
    
    // 用于接收来自客户端的消息
    private final Sinks.Many<JsonRpcMessage> incomingMessageSink;
    
    private LocalDateTime lastActivity;
    
    public SseServerTransport(String connectionId) {
        this.connectionId = connectionId;
        this.objectMapper = new ObjectMapper();
        this.lastActivity = LocalDateTime.now();
        
        // 初始化Sinks
        this.messageSink = Sinks.many().multicast().onBackpressureBuffer();
        this.errorSink = Sinks.many().multicast().onBackpressureBuffer();
        this.connectionStatusSink = Sinks.many().multicast().onBackpressureBuffer();
        this.incomingMessageSink = Sinks.many().multicast().onBackpressureBuffer();
    }
    
    @Override
    public String getType() {
        return "sse-server";
    }
    
    @Override
    public Mono<Void> connect() {
        if (connected.get()) {
            return Mono.empty();
        }
        
        return Mono.fromRunnable(() -> {
            log.info("建立SSE服务器传输连接: {}", connectionId);
            connected.set(true);
            connectionStatusSink.tryEmitNext(true);
            updateActivity();
            log.info("SSE服务器传输连接已建立: {}", connectionId);
        });
    }
    
    @Override
    public Mono<Void> disconnect() {
        if (!connected.get()) {
            return Mono.empty();
        }
        
        return Mono.fromRunnable(() -> {
            log.info("断开SSE服务器传输连接: {}", connectionId);
            connected.set(false);
            connectionStatusSink.tryEmitNext(false);
            messageSink.tryEmitComplete();
            incomingMessageSink.tryEmitComplete();
            log.info("SSE服务器传输连接已断开: {}", connectionId);
        });
    }
    
    @Override
    public boolean isConnected() {
        return connected.get();
    }
    
    @Override
    public Mono<Void> sendRequest(JsonRpcRequest request) {
        return sendMessage(request);
    }
    
    @Override
    public Mono<Void> sendResponse(JsonRpcResponse response) {
        return sendMessage(response);
    }
    
    @Override
    public Mono<Void> sendNotification(JsonRpcNotification notification) {
        return sendMessage(notification);
    }
    
    /**
     * 发送消息到客户端
     */
    private Mono<Void> sendMessage(JsonRpcMessage message) {
        if (!connected.get()) {
            return Mono.error(new IllegalStateException("SSE服务器传输未连接"));
        }
        
        return Mono.fromRunnable(() -> {
            try {
                log.debug("发送SSE消息到客户端: {} -> {}", connectionId, message);
                messageSink.tryEmitNext(message);
                updateActivity();
            } catch (Exception e) {
                log.error("发送SSE消息失败", e);
                errorSink.tryEmitNext(e);
                throw new RuntimeException("发送SSE消息失败", e);
            }
        });
    }
    
    @Override
    public Flux<JsonRpcMessage> receiveMessages() {
        return incomingMessageSink.asFlux();
    }
    
    @Override
    public Flux<Throwable> getErrors() {
        return errorSink.asFlux();
    }
    
    @Override
    public Flux<Boolean> getConnectionStatus() {
        return connectionStatusSink.asFlux();
    }
    
    /**
     * 获取发送到客户端的消息流
     * 这个方法用于SSE控制器获取要发送给客户端的消息
     */
    public Flux<String> getOutgoingMessageStream() {
        return messageSink.asFlux()
                .map(this::serializeMessage)
                .doOnNext(json -> log.debug("序列化SSE消息: {}", json))
                .doOnError(error -> log.error("序列化SSE消息失败", error));
    }
    
    /**
     * 接收来自客户端的消息
     * 这个方法用于处理客户端通过HTTP POST发送的消息
     */
    public Mono<Void> receiveFromClient(String messageJson) {
        if (!connected.get()) {
            return Mono.error(new IllegalStateException("SSE服务器传输未连接"));
        }
        
        return Mono.fromRunnable(() -> {
            try {
                log.debug("接收来自客户端的消息: {} <- {}", connectionId, messageJson);
                JsonRpcMessage message = parseJsonRpcMessage(messageJson);
                if (message != null) {
                    incomingMessageSink.tryEmitNext(message);
                    updateActivity();
                }
            } catch (Exception e) {
                log.error("处理客户端消息失败: {}", messageJson, e);
                errorSink.tryEmitNext(e);
            }
        });
    }
    
    /**
     * 获取连接ID
     */
    public String getConnectionId() {
        return connectionId;
    }
    
    /**
     * 获取最后活动时间
     */
    public LocalDateTime getLastActivity() {
        return lastActivity;
    }
    
    /**
     * 序列化消息为JSON字符串
     */
    private String serializeMessage(JsonRpcMessage message) {
        try {
            return objectMapper.writeValueAsString(message);
        } catch (JsonProcessingException e) {
            log.error("序列化消息失败", e);
            return "{\"error\":\"序列化失败\"}";
        }
    }
    
    /**
     * 解析JSON-RPC消息
     */
    private JsonRpcMessage parseJsonRpcMessage(String json) {
        try {
            // 首先尝试解析为通用的JsonRpcMessage
            if (json.contains("\"id\"") && json.contains("\"result\"")) {
                return objectMapper.readValue(json, JsonRpcResponse.class);
            } else if (json.contains("\"id\"") && json.contains("\"method\"")) {
                return objectMapper.readValue(json, JsonRpcRequest.class);
            } else if (json.contains("\"method\"")) {
                return (JsonRpcMessage) objectMapper.readValue(json, JsonRpcNotification.class);
            }
            
            log.warn("无法识别的JSON-RPC消息格式: {}", json);
            return null;
        } catch (JsonProcessingException e) {
            log.error("解析JSON-RPC消息失败: {}", json, e);
            return null;
        }
    }
    
    /**
     * 更新活动时间
     */
    private void updateActivity() {
        this.lastActivity = LocalDateTime.now();
    }
    
    /**
     * 检查连接是否超时
     */
    public boolean isTimeout(long timeoutMinutes) {
        if (lastActivity == null) {
            return false;
        }
        return lastActivity.isBefore(LocalDateTime.now().minusMinutes(timeoutMinutes));
    }
    
    /**
     * 强制关闭连接
     */
    public void forceClose() {
        disconnect().subscribe();
    }
}
