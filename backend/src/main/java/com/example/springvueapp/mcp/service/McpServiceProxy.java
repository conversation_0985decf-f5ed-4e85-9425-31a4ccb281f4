package com.example.springvueapp.mcp.service;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.model.McpServerStatus;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * MCP服务代理抽象接口
 * 定义了MCP服务器代理的核心功能
 */
public interface McpServiceProxy {
    
    /**
     * 启动MCP服务器实例
     * 
     * @param configuration MCP服务器配置
     * @param userId 用户ID
     * @return 启动的服务器实例
     */
    Mono<McpServerInstance> startMcpServer(McpServerConfiguration configuration, Long userId);
    
    /**
     * 停止MCP服务器实例
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 停止完成信号
     */
    Mono<Void> stopMcpServer(String sandboxId, Long userId);
    
    /**
     * 重启MCP服务器实例
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 重启后的服务器实例
     */
    Mono<McpServerInstance> restartMcpServer(String sandboxId, Long userId);
    
    /**
     * 获取MCP服务器状态
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 服务器状态
     */
    Mono<McpServerStatus> getMcpServerStatus(String sandboxId, Long userId);
    
    /**
     * 向MCP服务器发送JSON-RPC请求
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @param request JSON-RPC请求
     * @return JSON-RPC响应
     */
    Mono<JsonRpcResponse> sendMcpRequest(String sandboxId, Long userId, JsonRpcRequest request);
    
    /**
     * 获取MCP服务器的消息流
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 消息流
     */
    Flux<String> getMcpMessageStream(String sandboxId, Long userId);
    
    /**
     * 检查用户是否有权限访问指定的MCP服务器
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    Mono<Boolean> hasAccess(String sandboxId, Long userId);
    
    /**
     * 获取用户的所有MCP服务器实例
     * 
     * @param userId 用户ID
     * @return 服务器实例列表
     */
    Flux<McpServerInstance> getUserMcpServers(Long userId);
    
    /**
     * 清理用户的所有MCP服务器实例
     * 
     * @param userId 用户ID
     * @return 清理完成信号
     */
    Mono<Void> cleanupUserMcpServers(Long userId);
    
    /**
     * 获取代理类型
     * 
     * @return 代理类型标识
     */
    String getProxyType();
}
