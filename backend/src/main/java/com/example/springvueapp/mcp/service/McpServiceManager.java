package com.example.springvueapp.mcp.service;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.model.McpServerStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP服务管理器
 * 协调和管理不同类型的MCP服务代理
 */
@Service
public class McpServiceManager {
    
    private static final Logger log = LoggerFactory.getLogger(McpServiceManager.class);
    
    // 注册的服务代理
    private final Map<String, McpServiceProxy> registeredProxies = new ConcurrentHashMap<>();
    
    // 默认代理
    private McpServiceProxy defaultProxy;
    
    public McpServiceManager(List<McpServiceProxy> proxies) {
        // 注册所有可用的代理
        for (McpServiceProxy proxy : proxies) {
            registeredProxies.put(proxy.getProxyType(), proxy);
            log.info("注册MCP服务代理: {}", proxy.getProxyType());
        }
        
        // 设置默认代理
        this.defaultProxy = proxies.stream()
                .filter(proxy -> "default-docker".equals(proxy.getProxyType()))
                .findFirst()
                .orElse(proxies.isEmpty() ? null : proxies.get(0));
        
        if (defaultProxy != null) {
            log.info("设置默认MCP服务代理: {}", defaultProxy.getProxyType());
        } else {
            log.warn("未找到可用的MCP服务代理");
        }
    }
    
    /**
     * 启动MCP服务器
     * 
     * @param configuration MCP服务器配置
     * @param userId 用户ID
     * @return 启动的服务器实例
     */
    public Mono<McpServerInstance> startMcpServer(McpServerConfiguration configuration, Long userId) {
        McpServiceProxy proxy = getProxyForConfiguration(configuration);
        return proxy.startMcpServer(configuration, userId)
                .doOnSuccess(instance -> 
                    log.info("MCP服务器启动成功: {} (代理: {})", 
                            instance.getSandboxId(), proxy.getProxyType()));
    }
    
    /**
     * 停止MCP服务器
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 停止完成信号
     */
    public Mono<Void> stopMcpServer(String sandboxId, Long userId) {
        return findProxyForSandbox(sandboxId, userId)
                .flatMap(proxy -> proxy.stopMcpServer(sandboxId, userId))
                .doOnSuccess(v -> log.info("MCP服务器停止成功: {}", sandboxId));
    }
    
    /**
     * 重启MCP服务器
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 重启后的服务器实例
     */
    public Mono<McpServerInstance> restartMcpServer(String sandboxId, Long userId) {
        return findProxyForSandbox(sandboxId, userId)
                .flatMap(proxy -> proxy.restartMcpServer(sandboxId, userId))
                .doOnSuccess(instance -> log.info("MCP服务器重启成功: {}", sandboxId));
    }
    
    /**
     * 获取MCP服务器状态
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 服务器状态
     */
    public Mono<McpServerStatus> getMcpServerStatus(String sandboxId, Long userId) {
        return findProxyForSandbox(sandboxId, userId)
                .flatMap(proxy -> proxy.getMcpServerStatus(sandboxId, userId));
    }
    
    /**
     * 向MCP服务器发送请求
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @param request JSON-RPC请求
     * @return JSON-RPC响应
     */
    public Mono<JsonRpcResponse> sendMcpRequest(String sandboxId, Long userId, JsonRpcRequest request) {
        return findProxyForSandbox(sandboxId, userId)
                .flatMap(proxy -> proxy.sendMcpRequest(sandboxId, userId, request));
    }
    
    /**
     * 获取MCP服务器消息流
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 消息流
     */
    public Flux<String> getMcpMessageStream(String sandboxId, Long userId) {
        return findProxyForSandbox(sandboxId, userId)
                .flatMapMany(proxy -> proxy.getMcpMessageStream(sandboxId, userId));
    }
    
    /**
     * 获取用户的所有MCP服务器
     * 
     * @param userId 用户ID
     * @return 服务器实例列表
     */
    public Flux<McpServerInstance> getUserMcpServers(Long userId) {
        return Flux.fromIterable(registeredProxies.values())
                .flatMap(proxy -> proxy.getUserMcpServers(userId));
    }
    
    /**
     * 清理用户的所有MCP服务器
     * 
     * @param userId 用户ID
     * @return 清理完成信号
     */
    public Mono<Void> cleanupUserMcpServers(Long userId) {
        return Flux.fromIterable(registeredProxies.values())
                .flatMap(proxy -> proxy.cleanupUserMcpServers(userId))
                .then()
                .doOnSuccess(v -> log.info("用户MCP服务器清理完成: userId={}", userId));
    }
    
    /**
     * 检查访问权限
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    public Mono<Boolean> hasAccess(String sandboxId, Long userId) {
        return findProxyForSandbox(sandboxId, userId)
                .flatMap(proxy -> proxy.hasAccess(sandboxId, userId))
                .defaultIfEmpty(false);
    }
    
    /**
     * 注册新的服务代理
     * 
     * @param proxy 服务代理
     */
    public void registerProxy(McpServiceProxy proxy) {
        registeredProxies.put(proxy.getProxyType(), proxy);
        log.info("注册新的MCP服务代理: {}", proxy.getProxyType());
    }
    
    /**
     * 获取所有注册的代理类型
     * 
     * @return 代理类型列表
     */
    public List<String> getRegisteredProxyTypes() {
        return List.copyOf(registeredProxies.keySet());
    }
    
    /**
     * 根据配置选择合适的代理
     */
    private McpServiceProxy getProxyForConfiguration(McpServerConfiguration configuration) {
        // 这里可以根据配置的特定属性选择不同的代理
        // 目前简化处理，使用默认代理
        String proxyType = determineProxyType(configuration);
        
        McpServiceProxy proxy = registeredProxies.get(proxyType);
        if (proxy == null) {
            log.warn("未找到指定类型的代理: {}, 使用默认代理", proxyType);
            proxy = defaultProxy;
        }
        
        if (proxy == null) {
            throw new RuntimeException("没有可用的MCP服务代理");
        }
        
        return proxy;
    }
    
    /**
     * 为沙箱查找对应的代理
     */
    private Mono<McpServiceProxy> findProxyForSandbox(String sandboxId, Long userId) {
        // 简化处理：遍历所有代理查找能处理该沙箱的代理
        return Flux.fromIterable(registeredProxies.values())
                .filterWhen(proxy -> proxy.hasAccess(sandboxId, userId))
                .next()
                .switchIfEmpty(Mono.error(new RuntimeException("未找到处理该沙箱的代理: " + sandboxId)));
    }
    
    /**
     * 根据配置确定代理类型
     */
    private String determineProxyType(McpServerConfiguration configuration) {
        // 这里可以根据配置的不同属性来决定使用哪种代理
        // 例如：根据运行时类型、部署环境等
        
        if (configuration.getDockerImage() != null && !configuration.getDockerImage().isEmpty()) {
            return "default-docker";
        }
        
        // 可以添加更多的判断逻辑
        // if (configuration.getKubernetesConfig() != null) {
        //     return "kubernetes";
        // }
        
        return defaultProxy != null ? defaultProxy.getProxyType() : "default-docker";
    }
}
