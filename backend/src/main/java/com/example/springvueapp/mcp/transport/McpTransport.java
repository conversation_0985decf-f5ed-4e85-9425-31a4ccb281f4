package com.example.springvueapp.mcp.transport;

import com.example.springvueapp.mcp.protocol.JsonRpcMessage;
import com.example.springvueapp.mcp.protocol.JsonRpcNotification;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * MCP 传输层抽象接口
 * 支持不同的传输协议（stdio、SSE、WebSocket等）
 */
public interface McpTransport {
    
    /**
     * 获取传输类型
     * @return 传输类型（如 "stdio", "sse", "websocket"）
     */
    String getType();
    
    /**
     * 连接到MCP服务器
     * @return 连接完成信号
     */
    Mono<Void> connect();
    
    /**
     * 断开与MCP服务器的连接
     * @return 断开完成信号
     */
    Mono<Void> disconnect();
    
    /**
     * 检查连接状态
     * @return 是否已连接
     */
    boolean isConnected();
    
    /**
     * 发送JSON-RPC请求
     * @param request JSON-RPC请求
     * @return 发送完成信号
     */
    Mono<Void> sendRequest(JsonRpcRequest request);
    
    /**
     * 发送JSON-RPC响应
     * @param response JSON-RPC响应
     * @return 发送完成信号
     */
    Mono<Void> sendResponse(JsonRpcResponse response);
    
    /**
     * 发送JSON-RPC通知
     * @param notification JSON-RPC通知
     * @return 发送完成信号
     */
    Mono<Void> sendNotification(JsonRpcNotification notification);
    
    /**
     * 接收JSON-RPC消息流
     * @return 消息流
     */
    Flux<JsonRpcMessage> receiveMessages();
    
    /**
     * 获取错误流
     * @return 错误流
     */
    Flux<Throwable> getErrors();
    
    /**
     * 获取连接状态变化流
     * @return 连接状态流（true=已连接，false=已断开）
     */
    Flux<Boolean> getConnectionStatus();
}
