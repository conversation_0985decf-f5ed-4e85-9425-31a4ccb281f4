package com.example.springvueapp.mcp.service;

import com.example.springvueapp.mcp.client.McpClient;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.model.McpServerStatus;
import com.example.springvueapp.repository.McpServerInstanceRepository;
import com.example.springvueapp.sandbox.SandboxEnvironment;
import com.example.springvueapp.sandbox.SandboxInstance;
import com.example.springvueapp.service.McpConfigurationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认MCP服务代理实现
 * 基于Docker沙箱环境的MCP服务器代理
 */
@Service
public class DefaultMcpServiceProxy implements McpServiceProxy {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultMcpServiceProxy.class);
    
    private final McpServerInstanceRepository instanceRepository;
    private final McpConfigurationService configurationService;
    private final SandboxEnvironment sandboxEnvironment;
    private final ObjectMapper objectMapper;
    
    // 活跃的MCP客户端连接
    private final Map<String, McpClient> activeMcpClients = new ConcurrentHashMap<>();
    
    // 消息流管理
    private final Map<String, Sinks.Many<String>> messageStreams = new ConcurrentHashMap<>();
    
    // 服务器状态缓存
    private final Map<String, McpServerStatus> statusCache = new ConcurrentHashMap<>();
    
    public DefaultMcpServiceProxy(McpServerInstanceRepository instanceRepository,
                                 McpConfigurationService configurationService,
                                 SandboxEnvironment sandboxEnvironment,
                                 ObjectMapper objectMapper) {
        this.instanceRepository = instanceRepository;
        this.configurationService = configurationService;
        this.sandboxEnvironment = sandboxEnvironment;
        this.objectMapper = objectMapper;
    }
    
    @Override
    public Mono<McpServerInstance> startMcpServer(McpServerConfiguration configuration, Long userId) {
        log.info("启动MCP服务器: {} (用户: {})", configuration.getName(), userId);
        
        return validateConfiguration(configuration, userId)
                .flatMap(config -> createSandboxInstance(config))
                .flatMap(sandbox -> createMcpServerInstance(sandbox, configuration, userId))
                .flatMap(instance -> {
                    // 创建MCP客户端连接
                    return createMcpClient(instance.getSandboxId())
                            .flatMap(client -> {
                                activeMcpClients.put(instance.getSandboxId(), client);
                                setupMessageStream(instance.getSandboxId(), client);
                                updateServerStatus(instance.getSandboxId(), true);
                                return Mono.just(instance);
                            });
                })
                .doOnSuccess(instance -> 
                    log.info("MCP服务器启动成功: {} (沙箱: {})", 
                            configuration.getName(), instance.getSandboxId()))
                .doOnError(error -> 
                    log.error("MCP服务器启动失败: {}", configuration.getName(), error));
    }
    
    @Override
    public Mono<Void> stopMcpServer(String sandboxId, Long userId) {
        log.info("停止MCP服务器: {} (用户: {})", sandboxId, userId);
        
        return hasAccess(sandboxId, userId)
                .filter(hasAccess -> hasAccess)
                .switchIfEmpty(Mono.error(new RuntimeException("访问被拒绝")))
                .then(Mono.fromRunnable(() -> {
                    // 清理MCP客户端
                    McpClient client = activeMcpClients.remove(sandboxId);
                    if (client != null) {
                        client.close().subscribe();
                    }
                    
                    // 清理消息流
                    Sinks.Many<String> stream = messageStreams.remove(sandboxId);
                    if (stream != null) {
                        stream.tryEmitComplete();
                    }
                    
                    // 更新状态
                    updateServerStatus(sandboxId, false);
                }))
                .then(sandboxEnvironment.getSandbox(sandboxId))
                .flatMap(SandboxInstance::stop)
                .then(sandboxEnvironment.destroySandbox(sandboxId))
                .then(updateInstanceStatus(sandboxId, "STOPPED"))
                .doOnSuccess(v -> log.info("MCP服务器停止成功: {}", sandboxId))
                .doOnError(error -> log.error("MCP服务器停止失败: {}", sandboxId, error));
    }
    
    @Override
    public Mono<McpServerInstance> restartMcpServer(String sandboxId, Long userId) {
        log.info("重启MCP服务器: {} (用户: {})", sandboxId, userId);
        
        return instanceRepository.findBySandboxId(sandboxId)
                .switchIfEmpty(Mono.error(new RuntimeException("MCP服务器实例不存在")))
                .flatMap(instance -> {
                    if (!instance.getUserId().equals(userId)) {
                        return Mono.error(new RuntimeException("访问被拒绝"));
                    }
                    
                    return configurationService.getConfiguration(instance.getConfigurationId(), userId)
                            .flatMap(config -> stopMcpServer(sandboxId, userId)
                                    .then(startMcpServer(config, userId)));
                });
    }
    
    @Override
    public Mono<McpServerStatus> getMcpServerStatus(String sandboxId, Long userId) {
        return hasAccess(sandboxId, userId)
                .filter(hasAccess -> hasAccess)
                .switchIfEmpty(Mono.error(new RuntimeException("访问被拒绝")))
                .map(hasAccess -> {
                    McpServerStatus status = statusCache.get(sandboxId);
                    if (status == null) {
                        status = new McpServerStatus(sandboxId, false);
                        statusCache.put(sandboxId, status);
                    }
                    
                    // 更新连接状态
                    McpClient client = activeMcpClients.get(sandboxId);
                    if (client != null) {
                        status.setConnected(client.isConnected());
                    }
                    
                    return status;
                });
    }
    
    @Override
    public Mono<JsonRpcResponse> sendMcpRequest(String sandboxId, Long userId, JsonRpcRequest request) {
        return hasAccess(sandboxId, userId)
                .filter(hasAccess -> hasAccess)
                .switchIfEmpty(Mono.error(new RuntimeException("访问被拒绝")))
                .then(Mono.fromCallable(() -> activeMcpClients.get(sandboxId)))
                .switchIfEmpty(Mono.error(new RuntimeException("MCP客户端未连接")))
                .flatMap(client -> client.sendRequest(request))
                .doOnSuccess(response -> updateServerStatus(sandboxId, true))
                .doOnError(error -> {
                    log.error("发送MCP请求失败: sandboxId={}, method={}", 
                            sandboxId, request.getMethod(), error);
                    updateServerStatus(sandboxId, false);
                });
    }
    
    @Override
    public Flux<String> getMcpMessageStream(String sandboxId, Long userId) {
        return hasAccess(sandboxId, userId)
                .filter(hasAccess -> hasAccess)
                .switchIfEmpty(Mono.error(new RuntimeException("访问被拒绝")))
                .flatMapMany(hasAccess -> {
                    Sinks.Many<String> stream = messageStreams.get(sandboxId);
                    if (stream == null) {
                        return Flux.error(new RuntimeException("消息流不存在"));
                    }
                    return stream.asFlux();
                });
    }
    
    @Override
    public Mono<Boolean> hasAccess(String sandboxId, Long userId) {
        return instanceRepository.findBySandboxId(sandboxId)
                .map(instance -> instance.getUserId().equals(userId))
                .defaultIfEmpty(false);
    }
    
    @Override
    public Flux<McpServerInstance> getUserMcpServers(Long userId) {
        return instanceRepository.findByUserId(userId)
                .map(entity -> {
                    // 这里需要实现Entity到DTO的转换
                    // 简化处理，实际项目中应该使用Mapper
                    McpServerInstance instance = new McpServerInstance();
                    instance.setId(entity.getId());
                    instance.setSandboxId(entity.getSandboxId());
                    instance.setConfigurationId(entity.getConfigurationId());
                    instance.setUserId(entity.getUserId());
                    instance.setStatus(entity.getStatus());
                    instance.setCreatedAt(entity.getCreatedAt());
                    instance.setUpdatedAt(entity.getUpdatedAt());
                    return instance;
                });
    }
    
    @Override
    public Mono<Void> cleanupUserMcpServers(Long userId) {
        return getUserMcpServers(userId)
                .flatMap(instance -> stopMcpServer(instance.getSandboxId(), userId))
                .then();
    }
    
    @Override
    public String getProxyType() {
        return "default-docker";
    }
    
    /**
     * 验证配置
     */
    private Mono<McpServerConfiguration> validateConfiguration(McpServerConfiguration configuration, Long userId) {
        if (!configuration.getUserId().equals(userId)) {
            return Mono.error(new RuntimeException("配置访问被拒绝"));
        }
        
        if (!configuration.getEnabled()) {
            return Mono.error(new RuntimeException("配置未启用"));
        }
        
        return Mono.just(configuration);
    }
    
    /**
     * 创建沙箱实例
     */
    private Mono<SandboxInstance> createSandboxInstance(McpServerConfiguration configuration) {
        // 这里需要将配置转换为沙箱配置
        // 简化处理，实际实现应该更完整
        return Mono.error(new RuntimeException("需要实现沙箱配置转换"));
    }
    
    /**
     * 创建MCP服务器实例记录
     */
    private Mono<McpServerInstance> createMcpServerInstance(SandboxInstance sandbox, 
                                                           McpServerConfiguration configuration, 
                                                           Long userId) {
        // 这里需要创建数据库记录
        // 简化处理，实际实现应该保存到数据库
        McpServerInstance instance = new McpServerInstance();
        instance.setSandboxId(sandbox.getId());
        instance.setConfigurationId(configuration.getId());
        instance.setUserId(userId);
        instance.setCreatedAt(LocalDateTime.now());
        instance.setUpdatedAt(LocalDateTime.now());
        
        return Mono.just(instance);
    }
    
    /**
     * 创建MCP客户端
     */
    private Mono<McpClient> createMcpClient(String sandboxId) {
        return sandboxEnvironment.getSandbox(sandboxId)
                .map(McpClient::new)
                .flatMap(client -> client.connect().thenReturn(client));
    }
    
    /**
     * 设置消息流
     */
    private void setupMessageStream(String sandboxId, McpClient client) {
        Sinks.Many<String> stream = Sinks.many().multicast().onBackpressureBuffer();
        messageStreams.put(sandboxId, stream);
        
        client.receiveMessages()
                .subscribe(
                    message -> {
                        try {
                            String json = objectMapper.writeValueAsString(message);
                            stream.tryEmitNext(json);
                        } catch (Exception e) {
                            log.error("序列化消息失败", e);
                        }
                    },
                    error -> {
                        log.error("消息流错误: sandboxId={}", sandboxId, error);
                        stream.tryEmitError(error);
                    },
                    () -> {
                        log.debug("消息流完成: sandboxId={}", sandboxId);
                        stream.tryEmitComplete();
                    }
                );
    }
    
    /**
     * 更新服务器状态
     */
    private void updateServerStatus(String sandboxId, boolean connected) {
        McpServerStatus status = statusCache.computeIfAbsent(sandboxId, 
                k -> new McpServerStatus(sandboxId, false));
        status.setConnected(connected);
        status.setLastActivity(LocalDateTime.now());
    }
    
    /**
     * 更新实例状态
     */
    private Mono<Void> updateInstanceStatus(String sandboxId, String status) {
        return instanceRepository.findBySandboxId(sandboxId)
                .flatMap(instance -> {
                    // 这里需要更新状态枚举
                    // 简化处理
                    instance.setUpdatedAt(LocalDateTime.now());
                    return instanceRepository.save(instance);
                })
                .then();
    }
}
