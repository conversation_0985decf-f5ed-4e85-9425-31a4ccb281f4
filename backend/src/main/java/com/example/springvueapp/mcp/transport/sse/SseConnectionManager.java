package com.example.springvueapp.mcp.transport.sse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SSE连接管理器
 * 管理所有活跃的SSE连接，包括连接的创建、销毁和超时清理
 */
@Component
public class SseConnectionManager {
    
    private static final Logger log = LoggerFactory.getLogger(SseConnectionManager.class);
    
    // 存储所有活跃的SSE连接
    private final Map<String, SseServerTransport> activeConnections = new ConcurrentHashMap<>();
    
    // 连接超时时间（分钟）
    private static final long CONNECTION_TIMEOUT_MINUTES = 30;
    
    /**
     * 创建新的SSE连接
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return SSE服务器传输
     */
    public Mono<SseServerTransport> createConnection(String sandboxId, Long userId) {
        String connectionId = generateConnectionId(sandboxId, userId);
        
        return Mono.fromCallable(() -> {
            // 检查是否已存在连接
            SseServerTransport existingConnection = activeConnections.get(connectionId);
            if (existingConnection != null && existingConnection.isConnected()) {
                log.debug("重用现有SSE连接: {}", connectionId);
                return existingConnection;
            }
            
            // 创建新连接
            SseServerTransport transport = new SseServerTransport(connectionId);
            activeConnections.put(connectionId, transport);
            
            log.info("创建新的SSE连接: {} (沙箱: {}, 用户: {})", connectionId, sandboxId, userId);
            
            return transport;
        })
        .flatMap(transport -> transport.connect().thenReturn(transport));
    }
    
    /**
     * 获取现有的SSE连接
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return SSE服务器传输，如果不存在则返回空
     */
    public Mono<SseServerTransport> getConnection(String sandboxId, Long userId) {
        String connectionId = generateConnectionId(sandboxId, userId);
        
        return Mono.fromCallable(() -> {
            SseServerTransport transport = activeConnections.get(connectionId);
            if (transport != null && transport.isConnected()) {
                return transport;
            }
            return null;
        });
    }
    
    /**
     * 关闭指定的SSE连接
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 关闭完成信号
     */
    public Mono<Void> closeConnection(String sandboxId, Long userId) {
        String connectionId = generateConnectionId(sandboxId, userId);
        
        return Mono.fromCallable(() -> activeConnections.remove(connectionId))
                .flatMap(transport -> {
                    if (transport != null) {
                        log.info("关闭SSE连接: {}", connectionId);
                        return transport.disconnect();
                    }
                    return Mono.empty();
                });
    }
    
    /**
     * 关闭用户的所有SSE连接
     * 
     * @param userId 用户ID
     * @return 关闭完成信号
     */
    public Mono<Void> closeUserConnections(Long userId) {
        return Flux.fromIterable(activeConnections.entrySet())
                .filter(entry -> entry.getKey().endsWith(":" + userId))
                .flatMap(entry -> {
                    String connectionId = entry.getKey();
                    SseServerTransport transport = entry.getValue();
                    
                    activeConnections.remove(connectionId);
                    log.info("关闭用户SSE连接: {} (用户: {})", connectionId, userId);
                    
                    return transport.disconnect();
                })
                .then();
    }
    
    /**
     * 获取所有活跃连接的数量
     * 
     * @return 活跃连接数
     */
    public int getActiveConnectionCount() {
        return (int) activeConnections.values().stream()
                .filter(SseServerTransport::isConnected)
                .count();
    }
    
    /**
     * 获取指定用户的活跃连接数量
     * 
     * @param userId 用户ID
     * @return 用户的活跃连接数
     */
    public int getUserActiveConnectionCount(Long userId) {
        return (int) activeConnections.entrySet().stream()
                .filter(entry -> entry.getKey().endsWith(":" + userId))
                .filter(entry -> entry.getValue().isConnected())
                .count();
    }
    
    /**
     * 获取指定沙箱的活跃连接数量
     * 
     * @param sandboxId 沙箱ID
     * @return 沙箱的活跃连接数
     */
    public int getSandboxActiveConnectionCount(String sandboxId) {
        return (int) activeConnections.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(sandboxId + ":"))
                .filter(entry -> entry.getValue().isConnected())
                .count();
    }
    
    /**
     * 检查用户是否有权限访问指定的连接
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    public boolean hasAccess(String sandboxId, Long userId) {
        String connectionId = generateConnectionId(sandboxId, userId);
        return activeConnections.containsKey(connectionId);
    }
    
    /**
     * 定期清理超时的连接
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void cleanupTimeoutConnections() {
        log.debug("开始清理超时的SSE连接");
        
        activeConnections.entrySet().removeIf(entry -> {
            String connectionId = entry.getKey();
            SseServerTransport transport = entry.getValue();
            
            if (!transport.isConnected() || transport.isTimeout(CONNECTION_TIMEOUT_MINUTES)) {
                log.info("清理超时的SSE连接: {}", connectionId);
                transport.forceClose();
                return true;
            }
            
            return false;
        });
        
        log.debug("SSE连接清理完成，当前活跃连接数: {}", getActiveConnectionCount());
    }
    
    /**
     * 获取所有连接的统计信息
     * 
     * @return 连接统计信息
     */
    public Map<String, Object> getConnectionStats() {
        int totalConnections = activeConnections.size();
        int activeConnections = getActiveConnectionCount();
        int inactiveConnections = totalConnections - activeConnections;
        
        return Map.of(
            "totalConnections", totalConnections,
            "activeConnections", activeConnections,
            "inactiveConnections", inactiveConnections,
            "lastCleanup", LocalDateTime.now()
        );
    }
    
    /**
     * 强制关闭所有连接
     * 用于应用程序关闭时的清理
     */
    public void closeAllConnections() {
        log.info("关闭所有SSE连接，总数: {}", activeConnections.size());
        
        activeConnections.values().forEach(transport -> {
            try {
                transport.forceClose();
            } catch (Exception e) {
                log.warn("关闭SSE连接时发生错误", e);
            }
        });
        
        activeConnections.clear();
        log.info("所有SSE连接已关闭");
    }
    
    /**
     * 生成连接ID
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 连接ID
     */
    private String generateConnectionId(String sandboxId, Long userId) {
        return sandboxId + ":" + userId;
    }
}
