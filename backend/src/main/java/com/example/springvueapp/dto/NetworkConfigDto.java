package com.example.springvueapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 网络配置前端DTO
 */
public class NetworkConfigDto {
    
    @JsonProperty("networkMode")
    private String networkMode; // none, bridge, host, custom
    
    @JsonProperty("customNetworkName")
    private String customNetworkName;
    
    @JsonProperty("portMappings")
    private List<PortMappingDto> portMappings;
    
    @JsonProperty("allowedHosts")
    private List<String> allowedHosts;
    
    @JsonProperty("blockedHosts")
    private List<String> blockedHosts;
    
    @JsonProperty("dnsServers")
    private List<String> dnsServers;
    
    @JsonProperty("enableInternet")
    private Boolean enableInternet;
    
    // 构造函数
    public NetworkConfigDto() {
        this.networkMode = "none"; // 默认隔离
        this.enableInternet = false;
    }
    
    public NetworkConfigDto(String networkMode, Boolean enableInternet) {
        this.networkMode = networkMode;
        this.enableInternet = enableInternet;
    }
    
    // 内部类：端口映射
    public static class PortMappingDto {
        @JsonProperty("hostPort")
        private Integer hostPort;
        
        @JsonProperty("containerPort")
        private Integer containerPort;
        
        @JsonProperty("protocol")
        private String protocol; // tcp, udp
        
        public PortMappingDto() {
            this.protocol = "tcp";
        }
        
        public PortMappingDto(Integer hostPort, Integer containerPort, String protocol) {
            this.hostPort = hostPort;
            this.containerPort = containerPort;
            this.protocol = protocol != null ? protocol : "tcp";
        }
        
        public Integer getHostPort() {
            return hostPort;
        }
        
        public void setHostPort(Integer hostPort) {
            this.hostPort = hostPort;
        }
        
        public Integer getContainerPort() {
            return containerPort;
        }
        
        public void setContainerPort(Integer containerPort) {
            this.containerPort = containerPort;
        }
        
        public String getProtocol() {
            return protocol;
        }
        
        public void setProtocol(String protocol) {
            this.protocol = protocol;
        }
        
        @Override
        public String toString() {
            return "PortMappingDto{" +
                    "hostPort=" + hostPort +
                    ", containerPort=" + containerPort +
                    ", protocol='" + protocol + '\'' +
                    '}';
        }
    }
    
    // Getter和Setter方法
    public String getNetworkMode() {
        return networkMode;
    }
    
    public void setNetworkMode(String networkMode) {
        this.networkMode = networkMode;
    }
    
    public String getCustomNetworkName() {
        return customNetworkName;
    }
    
    public void setCustomNetworkName(String customNetworkName) {
        this.customNetworkName = customNetworkName;
    }
    
    public List<PortMappingDto> getPortMappings() {
        return portMappings;
    }
    
    public void setPortMappings(List<PortMappingDto> portMappings) {
        this.portMappings = portMappings;
    }
    
    public List<String> getAllowedHosts() {
        return allowedHosts;
    }
    
    public void setAllowedHosts(List<String> allowedHosts) {
        this.allowedHosts = allowedHosts;
    }
    
    public List<String> getBlockedHosts() {
        return blockedHosts;
    }
    
    public void setBlockedHosts(List<String> blockedHosts) {
        this.blockedHosts = blockedHosts;
    }
    
    public List<String> getDnsServers() {
        return dnsServers;
    }
    
    public void setDnsServers(List<String> dnsServers) {
        this.dnsServers = dnsServers;
    }
    
    public Boolean getEnableInternet() {
        return enableInternet;
    }
    
    public void setEnableInternet(Boolean enableInternet) {
        this.enableInternet = enableInternet;
    }
    
    @Override
    public String toString() {
        return "NetworkConfigDto{" +
                "networkMode='" + networkMode + '\'' +
                ", enableInternet=" + enableInternet +
                ", portMappings=" + (portMappings != null ? portMappings.size() : 0) +
                '}';
    }
}
