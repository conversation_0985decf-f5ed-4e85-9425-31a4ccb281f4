package com.example.springvueapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

/**
 * 用户前端DTO
 * 用于前端与后端之间的数据传输
 */
public class UserDto {
    
    @JsonProperty("id")
    private Long id;
    
    @JsonProperty("username")
    private String username;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("displayName")
    private String displayName;
    
    @JsonProperty("avatar")
    private String avatar;
    
    @JsonProperty("role")
    private String role;
    
    @JsonProperty("enabled")
    private Boolean enabled;
    
    @JsonProperty("lastLoginAt")
    private LocalDateTime lastLoginAt;
    
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;
    
    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;
    
    @JsonProperty("preferences")
    private UserPreferencesDto preferences;
    
    @JsonProperty("statistics")
    private UserStatisticsDto statistics;
    
    // 构造函数
    public UserDto() {
        this.enabled = true;
        this.role = "USER";
    }
    
    public UserDto(String username, String email) {
        this();
        this.username = username;
        this.email = email;
        this.displayName = username;
    }
    
    // 内部类：用户偏好设置
    public static class UserPreferencesDto {
        @JsonProperty("language")
        private String language = "zh-CN";
        
        @JsonProperty("theme")
        private String theme = "light";
        
        @JsonProperty("timezone")
        private String timezone = "Asia/Shanghai";
        
        @JsonProperty("notifications")
        private Boolean notifications = true;
        
        // Getter和Setter方法
        public String getLanguage() {
            return language;
        }
        
        public void setLanguage(String language) {
            this.language = language;
        }
        
        public String getTheme() {
            return theme;
        }
        
        public void setTheme(String theme) {
            this.theme = theme;
        }
        
        public String getTimezone() {
            return timezone;
        }
        
        public void setTimezone(String timezone) {
            this.timezone = timezone;
        }
        
        public Boolean getNotifications() {
            return notifications;
        }
        
        public void setNotifications(Boolean notifications) {
            this.notifications = notifications;
        }
    }
    
    // 内部类：用户统计信息
    public static class UserStatisticsDto {
        @JsonProperty("totalServers")
        private Integer totalServers = 0;
        
        @JsonProperty("runningServers")
        private Integer runningServers = 0;
        
        @JsonProperty("totalConfigurations")
        private Integer totalConfigurations = 0;
        
        @JsonProperty("lastActivityAt")
        private LocalDateTime lastActivityAt;
        
        // Getter和Setter方法
        public Integer getTotalServers() {
            return totalServers;
        }
        
        public void setTotalServers(Integer totalServers) {
            this.totalServers = totalServers;
        }
        
        public Integer getRunningServers() {
            return runningServers;
        }
        
        public void setRunningServers(Integer runningServers) {
            this.runningServers = runningServers;
        }
        
        public Integer getTotalConfigurations() {
            return totalConfigurations;
        }
        
        public void setTotalConfigurations(Integer totalConfigurations) {
            this.totalConfigurations = totalConfigurations;
        }
        
        public LocalDateTime getLastActivityAt() {
            return lastActivityAt;
        }
        
        public void setLastActivityAt(LocalDateTime lastActivityAt) {
            this.lastActivityAt = lastActivityAt;
        }
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    public LocalDateTime getLastLoginAt() {
        return lastLoginAt;
    }
    
    public void setLastLoginAt(LocalDateTime lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public UserPreferencesDto getPreferences() {
        return preferences;
    }
    
    public void setPreferences(UserPreferencesDto preferences) {
        this.preferences = preferences;
    }
    
    public UserStatisticsDto getStatistics() {
        return statistics;
    }
    
    public void setStatistics(UserStatisticsDto statistics) {
        this.statistics = statistics;
    }
    
    @Override
    public String toString() {
        return "UserDto{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", displayName='" + displayName + '\'' +
                ", role='" + role + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}
