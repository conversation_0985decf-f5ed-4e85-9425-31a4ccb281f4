package com.example.springvueapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 资源限制前端DTO
 */
public class ResourceLimitsDto {
    
    @JsonProperty("memoryLimitBytes")
    private Long memoryLimitBytes;
    
    @JsonProperty("memoryLimitMB")
    private Integer memoryLimitMB; // 便于前端显示
    
    @JsonProperty("cpuLimit")
    private Double cpuLimit;
    
    @JsonProperty("diskLimitBytes")
    private Long diskLimitBytes;
    
    @JsonProperty("diskLimitMB")
    private Integer diskLimitMB; // 便于前端显示
    
    @JsonProperty("networkBandwidthLimit")
    private Long networkBandwidthLimit;
    
    @JsonProperty("maxProcesses")
    private Integer maxProcesses;
    
    @JsonProperty("maxOpenFiles")
    private Integer maxOpenFiles;
    
    // 构造函数
    public ResourceLimitsDto() {
    }
    
    public ResourceLimitsDto(Long memoryLimitBytes, Double cpuLimit) {
        this.memoryLimitBytes = memoryLimitBytes;
        this.cpuLimit = cpuLimit;
        if (memoryLimitBytes != null) {
            this.memoryLimitMB = (int) (memoryLimitBytes / (1024 * 1024));
        }
    }
    
    // Getter和Setter方法
    public Long getMemoryLimitBytes() {
        return memoryLimitBytes;
    }
    
    public void setMemoryLimitBytes(Long memoryLimitBytes) {
        this.memoryLimitBytes = memoryLimitBytes;
        if (memoryLimitBytes != null) {
            this.memoryLimitMB = (int) (memoryLimitBytes / (1024 * 1024));
        }
    }
    
    public Integer getMemoryLimitMB() {
        return memoryLimitMB;
    }
    
    public void setMemoryLimitMB(Integer memoryLimitMB) {
        this.memoryLimitMB = memoryLimitMB;
        if (memoryLimitMB != null) {
            this.memoryLimitBytes = memoryLimitMB * 1024L * 1024L;
        }
    }
    
    public Double getCpuLimit() {
        return cpuLimit;
    }
    
    public void setCpuLimit(Double cpuLimit) {
        this.cpuLimit = cpuLimit;
    }
    
    public Long getDiskLimitBytes() {
        return diskLimitBytes;
    }
    
    public void setDiskLimitBytes(Long diskLimitBytes) {
        this.diskLimitBytes = diskLimitBytes;
        if (diskLimitBytes != null) {
            this.diskLimitMB = (int) (diskLimitBytes / (1024 * 1024));
        }
    }
    
    public Integer getDiskLimitMB() {
        return diskLimitMB;
    }
    
    public void setDiskLimitMB(Integer diskLimitMB) {
        this.diskLimitMB = diskLimitMB;
        if (diskLimitMB != null) {
            this.diskLimitBytes = diskLimitMB * 1024L * 1024L;
        }
    }
    
    public Long getNetworkBandwidthLimit() {
        return networkBandwidthLimit;
    }
    
    public void setNetworkBandwidthLimit(Long networkBandwidthLimit) {
        this.networkBandwidthLimit = networkBandwidthLimit;
    }
    
    public Integer getMaxProcesses() {
        return maxProcesses;
    }
    
    public void setMaxProcesses(Integer maxProcesses) {
        this.maxProcesses = maxProcesses;
    }
    
    public Integer getMaxOpenFiles() {
        return maxOpenFiles;
    }
    
    public void setMaxOpenFiles(Integer maxOpenFiles) {
        this.maxOpenFiles = maxOpenFiles;
    }
    
    @Override
    public String toString() {
        return "ResourceLimitsDto{" +
                "memoryLimitMB=" + memoryLimitMB +
                ", cpuLimit=" + cpuLimit +
                ", diskLimitMB=" + diskLimitMB +
                ", maxProcesses=" + maxProcesses +
                '}';
    }
}
