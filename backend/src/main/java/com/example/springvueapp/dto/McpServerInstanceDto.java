package com.example.springvueapp.dto;

import com.example.springvueapp.sandbox.SandboxStatus;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

/**
 * MCP服务器实例前端DTO
 * 用于前端与后端之间的数据传输
 */
public class McpServerInstanceDto {
    
    @JsonProperty("id")
    private Long id;
    
    @JsonProperty("sandboxId")
    private String sandboxId;
    
    @JsonProperty("configurationId")
    private Long configurationId;
    
    @JsonProperty("configuration")
    private McpServerConfigurationDto configuration;
    
    @JsonProperty("userId")
    private Long userId;
    
    @JsonProperty("status")
    private SandboxStatus status;
    
    @JsonProperty("sandboxType")
    private String sandboxType;
    
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;
    
    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;
    
    @JsonProperty("startedAt")
    private LocalDateTime startedAt;
    
    @JsonProperty("stoppedAt")
    private LocalDateTime stoppedAt;
    
    @JsonProperty("lastError")
    private String lastError;
    
    @JsonProperty("restartCount")
    private Integer restartCount;
    
    @JsonProperty("isRunning")
    private Boolean isRunning;
    
    @JsonProperty("uptime")
    private Long uptime; // 运行时间（秒）
    
    // 构造函数
    public McpServerInstanceDto() {
        this.restartCount = 0;
        this.isRunning = false;
    }
    
    public McpServerInstanceDto(String sandboxId, Long configurationId, Long userId) {
        this();
        this.sandboxId = sandboxId;
        this.configurationId = configurationId;
        this.userId = userId;
        this.status = SandboxStatus.CREATED;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getSandboxId() {
        return sandboxId;
    }
    
    public void setSandboxId(String sandboxId) {
        this.sandboxId = sandboxId;
    }
    
    public Long getConfigurationId() {
        return configurationId;
    }
    
    public void setConfigurationId(Long configurationId) {
        this.configurationId = configurationId;
    }
    
    public McpServerConfigurationDto getConfiguration() {
        return configuration;
    }
    
    public void setConfiguration(McpServerConfigurationDto configuration) {
        this.configuration = configuration;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public SandboxStatus getStatus() {
        return status;
    }
    
    public void setStatus(SandboxStatus status) {
        this.status = status;
        this.isRunning = (status == SandboxStatus.RUNNING);
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getSandboxType() {
        return sandboxType;
    }
    
    public void setSandboxType(String sandboxType) {
        this.sandboxType = sandboxType;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public LocalDateTime getStartedAt() {
        return startedAt;
    }
    
    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
        if (startedAt != null) {
            this.isRunning = true;
        }
    }
    
    public LocalDateTime getStoppedAt() {
        return stoppedAt;
    }
    
    public void setStoppedAt(LocalDateTime stoppedAt) {
        this.stoppedAt = stoppedAt;
        if (stoppedAt != null) {
            this.isRunning = false;
        }
    }
    
    public String getLastError() {
        return lastError;
    }
    
    public void setLastError(String lastError) {
        this.lastError = lastError;
    }
    
    public Integer getRestartCount() {
        return restartCount;
    }
    
    public void setRestartCount(Integer restartCount) {
        this.restartCount = restartCount;
    }
    
    public Boolean getIsRunning() {
        return isRunning;
    }
    
    public void setIsRunning(Boolean isRunning) {
        this.isRunning = isRunning;
    }
    
    public Long getUptime() {
        if (startedAt != null && isRunning) {
            return java.time.Duration.between(startedAt, LocalDateTime.now()).getSeconds();
        }
        return uptime;
    }
    
    public void setUptime(Long uptime) {
        this.uptime = uptime;
    }
    
    /**
     * 增加重启次数
     */
    public void incrementRestartCount() {
        if (this.restartCount == null) {
            this.restartCount = 0;
        }
        this.restartCount++;
    }
    
    /**
     * 检查实例是否健康
     */
    public boolean isHealthy() {
        return isRunning && status == SandboxStatus.RUNNING && lastError == null;
    }
    
    @Override
    public String toString() {
        return "McpServerInstanceDto{" +
                "id=" + id +
                ", sandboxId='" + sandboxId + '\'' +
                ", configurationId=" + configurationId +
                ", userId=" + userId +
                ", status=" + status +
                ", isRunning=" + isRunning +
                ", restartCount=" + restartCount +
                '}';
    }
}
