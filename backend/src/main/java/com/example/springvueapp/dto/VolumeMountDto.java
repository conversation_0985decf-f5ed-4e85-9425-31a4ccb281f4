package com.example.springvueapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 卷挂载前端DTO
 */
public class VolumeMountDto {
    
    @JsonProperty("hostPath")
    private String hostPath;
    
    @JsonProperty("containerPath")
    private String containerPath;
    
    @JsonProperty("readOnly")
    private Boolean readOnly;
    
    @JsonProperty("type")
    private String type; // bind, volume, tmpfs
    
    @JsonProperty("size")
    private String size; // 对于tmpfs类型
    
    @JsonProperty("description")
    private String description; // 挂载描述
    
    // 构造函数
    public VolumeMountDto() {
        this.readOnly = false;
        this.type = "bind";
    }
    
    public VolumeMountDto(String hostPath, String containerPath, Boolean readOnly) {
        this();
        this.hostPath = hostPath;
        this.containerPath = containerPath;
        this.readOnly = readOnly != null ? readOnly : false;
    }
    
    public VolumeMountDto(String hostPath, String containerPath, Boolean readOnly, String type) {
        this(hostPath, containerPath, readOnly);
        this.type = type != null ? type : "bind";
    }
    
    // Getter和Setter方法
    public String getHostPath() {
        return hostPath;
    }
    
    public void setHostPath(String hostPath) {
        this.hostPath = hostPath;
    }
    
    public String getContainerPath() {
        return containerPath;
    }
    
    public void setContainerPath(String containerPath) {
        this.containerPath = containerPath;
    }
    
    public Boolean getReadOnly() {
        return readOnly;
    }
    
    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getSize() {
        return size;
    }
    
    public void setSize(String size) {
        this.size = size;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
     * 验证挂载配置是否有效
     */
    public boolean isValid() {
        if (containerPath == null || containerPath.trim().isEmpty()) {
            return false;
        }
        
        if ("bind".equals(type) && (hostPath == null || hostPath.trim().isEmpty())) {
            return false;
        }
        
        if ("tmpfs".equals(type) && hostPath != null) {
            return false; // tmpfs不应该有hostPath
        }
        
        return true;
    }
    
    /**
     * 获取挂载类型的显示名称
     */
    public String getTypeDisplayName() {
        switch (type != null ? type : "bind") {
            case "bind":
                return "绑定挂载";
            case "volume":
                return "数据卷";
            case "tmpfs":
                return "临时文件系统";
            default:
                return type;
        }
    }
    
    @Override
    public String toString() {
        return "VolumeMountDto{" +
                "hostPath='" + hostPath + '\'' +
                ", containerPath='" + containerPath + '\'' +
                ", readOnly=" + readOnly +
                ", type='" + type + '\'' +
                '}';
    }
}
