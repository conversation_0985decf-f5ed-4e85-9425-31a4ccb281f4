package com.example.springvueapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * MCP服务器配置前端DTO
 * 用于前端与后端之间的数据传输
 */
public class McpServerConfigurationDto {
    
    @JsonProperty("id")
    private Long id;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("dockerImage")
    private String dockerImage;
    
    @JsonProperty("command")
    private String command;
    
    @JsonProperty("arguments")
    private List<String> arguments;
    
    @JsonProperty("workingDirectory")
    private String workingDirectory;
    
    @JsonProperty("environment")
    private Map<String, String> environment;
    
    @JsonProperty("timeoutSeconds")
    private Integer timeoutSeconds;
    
    @JsonProperty("autoRestart")
    private Boolean autoRestart;
    
    @JsonProperty("enabled")
    private Boolean enabled;
    
    @JsonProperty("resourceLimits")
    private ResourceLimitsDto resourceLimits;
    
    @JsonProperty("networkConfig")
    private NetworkConfigDto networkConfig;
    
    @JsonProperty("volumeMounts")
    private List<VolumeMountDto> volumeMounts;
    
    @JsonProperty("userId")
    private Long userId;
    
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;
    
    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;
    
    // 构造函数
    public McpServerConfigurationDto() {
    }
    
    public McpServerConfigurationDto(String name, String description, String dockerImage) {
        this.name = name;
        this.description = description;
        this.dockerImage = dockerImage;
        this.enabled = true;
        this.autoRestart = false;
        this.timeoutSeconds = 300;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getDockerImage() {
        return dockerImage;
    }
    
    public void setDockerImage(String dockerImage) {
        this.dockerImage = dockerImage;
    }
    
    public String getCommand() {
        return command;
    }
    
    public void setCommand(String command) {
        this.command = command;
    }
    
    public List<String> getArguments() {
        return arguments;
    }
    
    public void setArguments(List<String> arguments) {
        this.arguments = arguments;
    }
    
    public String getWorkingDirectory() {
        return workingDirectory;
    }
    
    public void setWorkingDirectory(String workingDirectory) {
        this.workingDirectory = workingDirectory;
    }
    
    public Map<String, String> getEnvironment() {
        return environment;
    }
    
    public void setEnvironment(Map<String, String> environment) {
        this.environment = environment;
    }
    
    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    public Boolean getAutoRestart() {
        return autoRestart;
    }
    
    public void setAutoRestart(Boolean autoRestart) {
        this.autoRestart = autoRestart;
    }
    
    public Boolean getEnabled() {
        return enabled;
    }
    
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
    
    public ResourceLimitsDto getResourceLimits() {
        return resourceLimits;
    }
    
    public void setResourceLimits(ResourceLimitsDto resourceLimits) {
        this.resourceLimits = resourceLimits;
    }
    
    public NetworkConfigDto getNetworkConfig() {
        return networkConfig;
    }
    
    public void setNetworkConfig(NetworkConfigDto networkConfig) {
        this.networkConfig = networkConfig;
    }
    
    public List<VolumeMountDto> getVolumeMounts() {
        return volumeMounts;
    }
    
    public void setVolumeMounts(List<VolumeMountDto> volumeMounts) {
        this.volumeMounts = volumeMounts;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "McpServerConfigurationDto{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", dockerImage='" + dockerImage + '\'' +
                ", command='" + command + '\'' +
                ", enabled=" + enabled +
                ", userId=" + userId +
                '}';
    }
}
