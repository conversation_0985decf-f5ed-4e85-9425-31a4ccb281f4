package com.example.springvueapp.config;

import com.example.springvueapp.controller.McpSseController;
import com.example.springvueapp.service.McpSseService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;

import static org.springframework.web.reactive.function.server.RequestPredicates.*;

/**
 * MCP路由配置
 * 定义MCP服务的URL路由规则和处理器
 */
@Configuration
public class McpRoutingConfig {
    
    /**
     * 配置MCP相关的函数式路由
     * 提供更灵活的路由配置，支持动态路由和中间件
     */
    @Bean
    public RouterFunction<ServerResponse> mcpRoutes(McpSseService mcpSseService) {
        return RouterFunctions
                // MCP SSE事件流路由
                .route(GET("/api/mcp/sse/{sandboxId}/events")
                        .and(accept(MediaType.TEXT_EVENT_STREAM)),
                    request -> {
                        String sandboxId = request.pathVariable("sandboxId");
                        // 这里需要从请求中获取用户认证信息
                        // 简化处理，实际应该从Security Context获取
                        Long userId = 1L; // 临时硬编码
                        
                        return ServerResponse.ok()
                                .contentType(MediaType.TEXT_EVENT_STREAM)
                                .body(mcpSseService.streamMcpEvents(sandboxId, userId), String.class);
                    })
                
                // MCP服务器状态查询路由
                .andRoute(GET("/api/mcp/sse/{sandboxId}/status"),
                    request -> {
                        String sandboxId = request.pathVariable("sandboxId");
                        Long userId = 1L; // 临时硬编码
                        
                        return mcpSseService.getMcpServerStatus(sandboxId, userId)
                                .flatMap(status -> ServerResponse.ok()
                                        .contentType(MediaType.APPLICATION_JSON)
                                        .bodyValue(status))
                                .onErrorResume(error -> ServerResponse.badRequest()
                                        .bodyValue("获取状态失败: " + error.getMessage()));
                    })
                
                // MCP心跳检测路由
                .andRoute(POST("/api/mcp/sse/{sandboxId}/ping"),
                    request -> {
                        String sandboxId = request.pathVariable("sandboxId");
                        Long userId = 1L; // 临时硬编码
                        
                        return mcpSseService.pingMcpServer(sandboxId, userId)
                                .flatMap(latency -> ServerResponse.ok()
                                        .contentType(MediaType.APPLICATION_JSON)
                                        .bodyValue(java.util.Map.of(
                                                "success", true,
                                                "latency", latency,
                                                "timestamp", System.currentTimeMillis()
                                        )))
                                .onErrorResume(error -> ServerResponse.badRequest()
                                        .bodyValue(java.util.Map.of(
                                                "success", false,
                                                "error", error.getMessage(),
                                                "timestamp", System.currentTimeMillis()
                                        )));
                    })
                
                // 通用MCP请求发送路由
                .andRoute(POST("/api/mcp/sse/{sandboxId}/send"),
                    request -> {
                        String sandboxId = request.pathVariable("sandboxId");
                        Long userId = 1L; // 临时硬编码
                        
                        return request.bodyToMono(com.example.springvueapp.mcp.protocol.JsonRpcRequest.class)
                                .flatMap(jsonRpcRequest -> mcpSseService.sendMcpRequest(sandboxId, userId, jsonRpcRequest))
                                .then(ServerResponse.ok()
                                        .contentType(MediaType.APPLICATION_JSON)
                                        .bodyValue(java.util.Map.of(
                                                "success", true,
                                                "message", "请求已发送"
                                        )))
                                .onErrorResume(error -> ServerResponse.badRequest()
                                        .bodyValue(java.util.Map.of(
                                                "success", false,
                                                "error", error.getMessage()
                                        )));
                    })
                
                // MCP服务器管理路由 - 启动
                .andRoute(POST("/api/mcp/servers/{configId}/start"),
                    request -> {
                        Long configId = Long.parseLong(request.pathVariable("configId"));
                        Long userId = 1L; // 临时硬编码
                        
                        // 这里需要调用MCP服务管理器来启动服务器
                        return ServerResponse.ok()
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(java.util.Map.of(
                                        "success", true,
                                        "message", "服务器启动请求已提交",
                                        "configId", configId
                                ));
                    })
                
                // MCP服务器管理路由 - 停止
                .andRoute(POST("/api/mcp/servers/{sandboxId}/stop"),
                    request -> {
                        String sandboxId = request.pathVariable("sandboxId");
                        Long userId = 1L; // 临时硬编码
                        
                        // 这里需要调用MCP服务管理器来停止服务器
                        return ServerResponse.ok()
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(java.util.Map.of(
                                        "success", true,
                                        "message", "服务器停止请求已提交",
                                        "sandboxId", sandboxId
                                ));
                    })
                
                // MCP服务器管理路由 - 重启
                .andRoute(POST("/api/mcp/servers/{sandboxId}/restart"),
                    request -> {
                        String sandboxId = request.pathVariable("sandboxId");
                        Long userId = 1L; // 临时硬编码
                        
                        // 这里需要调用MCP服务管理器来重启服务器
                        return ServerResponse.ok()
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(java.util.Map.of(
                                        "success", true,
                                        "message", "服务器重启请求已提交",
                                        "sandboxId", sandboxId
                                ));
                    })
                
                // 获取用户的所有MCP服务器
                .andRoute(GET("/api/mcp/servers"),
                    request -> {
                        Long userId = 1L; // 临时硬编码
                        
                        // 这里需要调用MCP服务管理器来获取用户的服务器列表
                        return ServerResponse.ok()
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(java.util.Map.of(
                                        "success", true,
                                        "servers", java.util.List.of(),
                                        "userId", userId
                                ));
                    })
                
                // 健康检查路由
                .andRoute(GET("/api/mcp/health"),
                    request -> ServerResponse.ok()
                            .contentType(MediaType.APPLICATION_JSON)
                            .bodyValue(java.util.Map.of(
                                    "status", "UP",
                                    "service", "MCP SSE Service",
                                    "timestamp", System.currentTimeMillis()
                            )));
    }
}
