package com.example.springvueapp.mapper;

import com.example.springvueapp.entity.McpServerConfigurationEntity;
import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.NetworkConfig;
import com.example.springvueapp.model.ResourceLimits;
import com.example.springvueapp.model.VolumeMount;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * MCP配置实体和DTO之间的转换器
 */
@Component
public class McpConfigurationMapper {

    private static final Logger log = LoggerFactory.getLogger(McpConfigurationMapper.class);
    private final ObjectMapper objectMapper;

    public McpConfigurationMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 将Entity转换为DTO
     */
    public McpServerConfiguration toDto(McpServerConfigurationEntity entity) {
        if (entity == null) {
            return null;
        }

        return McpServerConfiguration.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .command(entity.getCommand())
                .arguments(parseArguments(entity.getArguments()))
                .environment(parseEnvironment(entity.getEnvironment()))
                .workingDirectory(entity.getWorkingDirectory())
                .dockerImage(entity.getDockerImage())
                .resourceLimits(parseResourceLimits(entity.getResourceLimits()))
                .networkConfig(parseNetworkConfig(entity.getNetworkConfig()))
                .volumeMounts(parseVolumeMounts(entity.getVolumeMounts()))
                .timeoutSeconds(entity.getTimeoutSeconds())
                .autoRestart(entity.getAutoRestart())
                .enabled(entity.getEnabled())
                .userId(entity.getUserId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为Entity
     */
    public McpServerConfigurationEntity toEntity(McpServerConfiguration dto) {
        if (dto == null) {
            return null;
        }

        return McpServerConfigurationEntity.builder()
                .id(dto.getId())
                .name(dto.getName())
                .description(dto.getDescription())
                .command(dto.getCommand())
                .arguments(serializeArguments(dto.getArguments()))
                .environment(serializeEnvironment(dto.getEnvironment()))
                .workingDirectory(dto.getWorkingDirectory())
                .dockerImage(dto.getDockerImage())
                .resourceLimits(serializeResourceLimits(dto.getResourceLimits()))
                .networkConfig(serializeNetworkConfig(dto.getNetworkConfig()))
                .volumeMounts(serializeVolumeMounts(dto.getVolumeMounts()))
                .timeoutSeconds(dto.getTimeoutSeconds())
                .autoRestart(dto.getAutoRestart())
                .enabled(dto.getEnabled())
                .userId(dto.getUserId())
                .createdAt(dto.getCreatedAt())
                .updatedAt(dto.getUpdatedAt())
                .build();
    }

    private List<String> parseArguments(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse arguments JSON: {}", json, e);
            return null;
        }
    }

    private String serializeArguments(List<String> arguments) {
        if (arguments == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(arguments);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize arguments: {}", arguments, e);
            return null;
        }
    }

    private Map<String, String> parseEnvironment(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, new TypeReference<Map<String, String>>() {});
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse environment JSON: {}", json, e);
            return null;
        }
    }

    private String serializeEnvironment(Map<String, String> environment) {
        if (environment == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(environment);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize environment: {}", environment, e);
            return null;
        }
    }

    private ResourceLimits parseResourceLimits(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, ResourceLimits.class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse resourceLimits JSON: {}", json, e);
            return null;
        }
    }

    private String serializeResourceLimits(ResourceLimits resourceLimits) {
        if (resourceLimits == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(resourceLimits);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize resourceLimits: {}", resourceLimits, e);
            return null;
        }
    }

    private NetworkConfig parseNetworkConfig(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, NetworkConfig.class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse networkConfig JSON: {}", json, e);
            return null;
        }
    }

    private String serializeNetworkConfig(NetworkConfig networkConfig) {
        if (networkConfig == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(networkConfig);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize networkConfig: {}", networkConfig, e);
            return null;
        }
    }

    private List<VolumeMount> parseVolumeMounts(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, new TypeReference<List<VolumeMount>>() {});
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse volumeMounts JSON: {}", json, e);
            return null;
        }
    }

    private String serializeVolumeMounts(List<VolumeMount> volumeMounts) {
        if (volumeMounts == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(volumeMounts);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize volumeMounts: {}", volumeMounts, e);
            return null;
        }
    }
}
