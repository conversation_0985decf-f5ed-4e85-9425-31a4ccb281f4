package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.service.McpServerLifecycleManager;
import com.example.springvueapp.service.McpSseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * MCP Server-Sent Events 控制器
 * 为外部MCP客户端提供基于SSE协议的MCP服务接口
 */
@RestController
@RequestMapping("/api/mcp/sse")
public class McpSseController {
    
    private static final Logger log = LoggerFactory.getLogger(McpSseController.class);
    
    private final McpSseService mcpSseService;
    private final McpServerLifecycleManager lifecycleManager;

    public McpSseController(McpSseService mcpSseService,
                           McpServerLifecycleManager lifecycleManager) {
        this.mcpSseService = mcpSseService;
        this.lifecycleManager = lifecycleManager;
    }
    
    /**
     * 建立SSE连接到指定的MCP服务器实例
     * URL格式: /api/mcp/sse/{sandboxId}/events
     * 
     * @param sandboxId MCP服务器沙箱ID
     * @param authentication 用户认证信息
     * @return SSE事件流
     */
    @GetMapping(value = "/{sandboxId}/events", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamMcpEvents(
            @PathVariable String sandboxId,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("建立SSE连接到MCP服务器: {} (用户: {})", sandboxId, userId);
        
        return mcpSseService.streamMcpEvents(sandboxId, userId)
                .map(message -> ServerSentEvent.<String>builder()
                        .id(String.valueOf(System.currentTimeMillis()))
                        .event("mcp-message")
                        .data(message)
                        .build())
                .doOnSubscribe(subscription -> 
                    log.debug("SSE订阅开始: sandboxId={}, userId={}", sandboxId, userId))
                .doOnCancel(() -> 
                    log.debug("SSE连接取消: sandboxId={}, userId={}", sandboxId, userId))
                .doOnComplete(() -> 
                    log.debug("SSE连接完成: sandboxId={}, userId={}", sandboxId, userId))
                .doOnError(error -> 
                    log.error("SSE连接错误: sandboxId={}, userId={}", sandboxId, userId, error));
    }
    
    /**
     * 向指定的MCP服务器发送JSON-RPC请求
     * URL格式: /api/mcp/sse/{sandboxId}/send
     * 
     * @param sandboxId MCP服务器沙箱ID
     * @param request JSON-RPC请求
     * @param authentication 用户认证信息
     * @return 发送结果
     */
    @PostMapping("/{sandboxId}/send")
    public Mono<Map<String, Object>> sendMcpRequest(
            @PathVariable String sandboxId,
            @RequestBody JsonRpcRequest request,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.debug("发送MCP请求到服务器: {} (用户: {}, 方法: {})", 
                sandboxId, userId, request.getMethod());
        
        return mcpSseService.sendMcpRequest(sandboxId, userId, request)
                .map(response -> Map.of(
                    "success", true,
                    "message", "请求已发送",
                    "requestId", request.getId() != null ? request.getId() : "unknown"
                ))
                .onErrorResume(error -> {
                    log.error("发送MCP请求失败: sandboxId={}, method={}", 
                            sandboxId, request.getMethod(), error);
                    return Mono.just(Map.of(
                        "success", false,
                        "error", error.getMessage()
                    ));
                });
    }
    
    /**
     * 获取MCP服务器连接状态
     * URL格式: /api/mcp/sse/{sandboxId}/status
     * 
     * @param sandboxId MCP服务器沙箱ID
     * @param authentication 用户认证信息
     * @return 连接状态信息
     */
    @GetMapping("/{sandboxId}/status")
    public Mono<Map<String, Object>> getMcpServerStatus(
            @PathVariable String sandboxId,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.debug("获取MCP服务器状态: {} (用户: {})", sandboxId, userId);
        
        return mcpSseService.getMcpServerStatus(sandboxId, userId)
                .map(status -> {
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("sandboxId", sandboxId);
                    result.put("connected", status.isConnected());
                    result.put("lastActivity", status.getLastActivity());
                    result.put("activeConnections", status.getActiveConnections());
                    return result;
                })
                .onErrorResume(error -> {
                    log.error("获取MCP服务器状态失败: sandboxId={}", sandboxId, error);
                    Map<String, Object> errorResult = new java.util.HashMap<>();
                    errorResult.put("sandboxId", sandboxId);
                    errorResult.put("connected", false);
                    errorResult.put("error", error.getMessage());
                    return Mono.just(errorResult);
                });
    }
    
    /**
     * 心跳检测端点
     * URL格式: /api/mcp/sse/{sandboxId}/ping
     * 
     * @param sandboxId MCP服务器沙箱ID
     * @param authentication 用户认证信息
     * @return 心跳响应
     */
    @PostMapping("/{sandboxId}/ping")
    public Mono<Map<String, Object>> pingMcpServer(
            @PathVariable String sandboxId,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        
        return mcpSseService.pingMcpServer(sandboxId, userId)
                .map(latency -> {
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("success", true);
                    result.put("latency", latency);
                    result.put("timestamp", System.currentTimeMillis());
                    return result;
                })
                .timeout(Duration.ofSeconds(5))
                .onErrorResume(error -> {
                    Map<String, Object> errorResult = new java.util.HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("error", error.getMessage());
                    errorResult.put("timestamp", System.currentTimeMillis());
                    return Mono.just(errorResult);
                });
    }
    
    /**
     * 启动MCP服务器
     * URL格式: /api/mcp/servers/{configId}/start
     *
     * @param configId 配置ID
     * @param authentication 用户认证信息
     * @return 启动结果
     */
    @PostMapping("/servers/{configId}/start")
    public Mono<Map<String, Object>> startMcpServer(
            @PathVariable Long configId,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        log.info("启动MCP服务器: configId={} (用户: {})", configId, userId);

        return lifecycleManager.startServer(configId, userId)
                .map(instance -> {
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("success", true);
                    result.put("message", "服务器启动成功");
                    result.put("instance", instance);
                    return result;
                })
                .onErrorResume(error -> {
                    log.error("启动MCP服务器失败: configId={}", configId, error);
                    Map<String, Object> errorResult = new java.util.HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("error", error.getMessage());
                    return Mono.just(errorResult);
                });
    }

    /**
     * 停止MCP服务器
     * URL格式: /api/mcp/servers/{sandboxId}/stop
     *
     * @param sandboxId 沙箱ID
     * @param authentication 用户认证信息
     * @return 停止结果
     */
    @PostMapping("/servers/{sandboxId}/stop")
    public Mono<Map<String, Object>> stopMcpServer(
            @PathVariable String sandboxId,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        log.info("停止MCP服务器: sandboxId={} (用户: {})", sandboxId, userId);

        return lifecycleManager.stopServer(sandboxId, userId)
                .then(Mono.fromCallable(() -> {
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("success", true);
                    result.put("message", "服务器停止成功");
                    result.put("sandboxId", sandboxId);
                    return result;
                }))
                .onErrorResume(error -> {
                    log.error("停止MCP服务器失败: sandboxId={}", sandboxId, error);
                    Map<String, Object> errorResult = new java.util.HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("error", error.getMessage());
                    return Mono.just(errorResult);
                });
    }

    /**
     * 重启MCP服务器
     * URL格式: /api/mcp/servers/{sandboxId}/restart
     *
     * @param sandboxId 沙箱ID
     * @param authentication 用户认证信息
     * @return 重启结果
     */
    @PostMapping("/servers/{sandboxId}/restart")
    public Mono<Map<String, Object>> restartMcpServer(
            @PathVariable String sandboxId,
            Authentication authentication) {

        Long userId = getUserId(authentication);
        log.info("重启MCP服务器: sandboxId={} (用户: {})", sandboxId, userId);

        return lifecycleManager.restartServer(sandboxId, userId)
                .map(instance -> {
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("success", true);
                    result.put("message", "服务器重启成功");
                    result.put("instance", instance);
                    return result;
                })
                .onErrorResume(error -> {
                    log.error("重启MCP服务器失败: sandboxId={}", sandboxId, error);
                    Map<String, Object> errorResult = new java.util.HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("error", error.getMessage());
                    return Mono.just(errorResult);
                });
    }

    /**
     * 获取用户的所有MCP服务器
     * URL格式: /api/mcp/servers
     *
     * @param authentication 用户认证信息
     * @return 服务器列表
     */
    @GetMapping("/servers")
    public Mono<Map<String, Object>> getUserMcpServers(Authentication authentication) {
        Long userId = getUserId(authentication);
        log.debug("获取用户MCP服务器列表: userId={}", userId);

        return lifecycleManager.getUserServers(userId)
                .collectList()
                .map(servers -> {
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("success", true);
                    result.put("servers", servers);
                    result.put("count", servers.size());
                    return result;
                })
                .onErrorResume(error -> {
                    log.error("获取用户MCP服务器列表失败: userId={}", userId, error);
                    Map<String, Object> errorResult = new java.util.HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("error", error.getMessage());
                    return Mono.just(errorResult);
                });
    }

    /**
     * 从认证信息中提取用户ID
     */
    private Long getUserId(Authentication authentication) {
        // 这里需要根据实际的认证实现来获取用户ID
        // 假设用户名就是用户ID，实际项目中可能需要查询用户表
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            // 如果用户名不是数字，可能需要通过用户服务查询
            // 这里简化处理，返回一个默认值或抛出异常
            log.warn("无法从认证信息中解析用户ID: {}", authentication.getName());
            return 1L; // 临时返回默认用户ID
        }
    }
}
