package com.example.springvueapp.security;

import com.example.springvueapp.service.JwtService;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.Collections;

/**
 * JWT认证过滤器，用于验证请求中的JWT token
 */
public class JwtAuthenticationWebFilter implements WebFilter {
    private final JwtService jwtService;

    public JwtAuthenticationWebFilter(JwtService jwtService) {
        this.jwtService = jwtService;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String token = extractTokenFromRequest(exchange);
        if (StringUtils.hasText(token)) {
            return jwtService.validateTokenMono(token)
                    .filter(isValid -> isValid)
                    .flatMap(isValid -> jwtService.getUsernameFromTokenMono(token))
                    .filter(StringUtils::hasText)
                    .map(username -> {
                        // 创建认证对象
                        return (Authentication) new UsernamePasswordAuthenticationToken(username, null, Collections.emptyList());
                    })
                    .flatMap(authentication ->
                            chain.filter(exchange).contextWrite(ReactiveSecurityContextHolder.withAuthentication(authentication))
                    )
                    .switchIfEmpty(chain.filter(exchange));
        }
        return chain.filter(exchange);
    }

    /**
     * 从请求头中提取JWT token
     */
    private String extractTokenFromRequest(ServerWebExchange exchange) {
        String bearerToken = exchange.getRequest().getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
