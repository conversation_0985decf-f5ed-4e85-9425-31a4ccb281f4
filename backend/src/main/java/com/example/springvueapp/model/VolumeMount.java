package com.example.springvueapp.model;

/**
 * 卷挂载配置
 * 与前端TypeScript接口保持一致
 */
public class VolumeMount {

    private String hostPath;
    private String containerPath;
    private Boolean readOnly;

    public VolumeMount() {
    }

    public VolumeMount(String hostPath, String containerPath, Boolean readOnly) {
        this.hostPath = hostPath;
        this.containerPath = containerPath;
        this.readOnly = readOnly;
    }

    public String getHostPath() {
        return hostPath;
    }

    public void setHostPath(String hostPath) {
        this.hostPath = hostPath;
    }

    public String getContainerPath() {
        return containerPath;
    }

    public void setContainerPath(String containerPath) {
        this.containerPath = containerPath;
    }

    public Boolean getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Boolean readOnly) {
        this.readOnly = readOnly;
    }
}
