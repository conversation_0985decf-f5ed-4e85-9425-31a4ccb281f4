package com.example.springvueapp.model;

import java.util.List;

/**
 * 网络配置
 * 与前端TypeScript接口保持一致
 */
public class NetworkConfig {

    private Boolean enableNetworking;
    private List<String> allowedHosts;
    private List<Integer> exposedPorts;

    public NetworkConfig() {
    }

    public NetworkConfig(Boolean enableNetworking, List<String> allowedHosts, List<Integer> exposedPorts) {
        this.enableNetworking = enableNetworking;
        this.allowedHosts = allowedHosts;
        this.exposedPorts = exposedPorts;
    }

    public Boolean getEnableNetworking() {
        return enableNetworking;
    }

    public void setEnableNetworking(Boolean enableNetworking) {
        this.enableNetworking = enableNetworking;
    }

    public List<String> getAllowedHosts() {
        return allowedHosts;
    }

    public void setAllowedHosts(List<String> allowedHosts) {
        this.allowedHosts = allowedHosts;
    }

    public List<Integer> getExposedPorts() {
        return exposedPorts;
    }

    public void setExposedPorts(List<Integer> exposedPorts) {
        this.exposedPorts = exposedPorts;
    }
}
