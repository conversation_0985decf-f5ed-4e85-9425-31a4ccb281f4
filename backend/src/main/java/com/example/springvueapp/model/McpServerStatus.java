package com.example.springvueapp.model;

import java.time.LocalDateTime;

/**
 * MCP服务器状态信息
 */
public class McpServerStatus {
    
    private String sandboxId;
    private boolean connected;
    private LocalDateTime lastActivity;
    private int activeConnections;
    private String lastError;
    private LocalDateTime createdAt;
    
    public McpServerStatus() {
        this.createdAt = LocalDateTime.now();
    }
    
    public McpServerStatus(String sandboxId, boolean connected) {
        this();
        this.sandboxId = sandboxId;
        this.connected = connected;
        this.lastActivity = LocalDateTime.now();
    }
    
    public String getSandboxId() {
        return sandboxId;
    }
    
    public void setSandboxId(String sandboxId) {
        this.sandboxId = sandboxId;
    }
    
    public boolean isConnected() {
        return connected;
    }
    
    public void setConnected(boolean connected) {
        this.connected = connected;
        if (connected) {
            this.lastActivity = LocalDateTime.now();
        }
    }
    
    public LocalDateTime getLastActivity() {
        return lastActivity;
    }
    
    public void setLastActivity(LocalDateTime lastActivity) {
        this.lastActivity = lastActivity;
    }
    
    public int getActiveConnections() {
        return activeConnections;
    }
    
    public void setActiveConnections(int activeConnections) {
        this.activeConnections = activeConnections;
    }
    
    public String getLastError() {
        return lastError;
    }
    
    public void setLastError(String lastError) {
        this.lastError = lastError;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "McpServerStatus{" +
                "sandboxId='" + sandboxId + '\'' +
                ", connected=" + connected +
                ", lastActivity=" + lastActivity +
                ", activeConnections=" + activeConnections +
                ", lastError='" + lastError + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
