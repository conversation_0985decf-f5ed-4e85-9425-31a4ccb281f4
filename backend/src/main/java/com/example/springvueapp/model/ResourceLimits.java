package com.example.springvueapp.model;

/**
 * 资源限制配置
 * 与前端TypeScript接口保持一致
 */
public class ResourceLimits {

    private Long memoryLimitBytes;
    private Double cpuLimit;
    private Long diskLimitBytes;

    public ResourceLimits() {
    }

    public ResourceLimits(Long memoryLimitBytes, Double cpuLimit, Long diskLimitBytes) {
        this.memoryLimitBytes = memoryLimitBytes;
        this.cpuLimit = cpuLimit;
        this.diskLimitBytes = diskLimitBytes;
    }

    public Long getMemoryLimitBytes() {
        return memoryLimitBytes;
    }

    public void setMemoryLimitBytes(Long memoryLimitBytes) {
        this.memoryLimitBytes = memoryLimitBytes;
    }

    public Double getCpuLimit() {
        return cpuLimit;
    }

    public void setCpuLimit(Double cpuLimit) {
        this.cpuLimit = cpuLimit;
    }

    public Long getDiskLimitBytes() {
        return diskLimitBytes;
    }

    public void setDiskLimitBytes(Long diskLimitBytes) {
        this.diskLimitBytes = diskLimitBytes;
    }
}
