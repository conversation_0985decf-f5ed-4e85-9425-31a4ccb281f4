package com.example.springvueapp.repository;

import com.example.springvueapp.entity.McpServerConfigurationEntity;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Repository for MCP server configurations
 */
@Repository
public interface McpServerConfigurationRepository extends R2dbcRepository<McpServerConfigurationEntity, Long> {
    
    /**
     * Find configurations by user ID
     */
    Flux<McpServerConfigurationEntity> findByUserId(Long userId);

    /**
     * Find configurations by user ID and enabled status
     */
    Flux<McpServerConfigurationEntity> findByUserIdAndEnabled(Long userId, Boolean enabled);

    /**
     * Find configuration by name and user ID
     */
    Mono<McpServerConfigurationEntity> findByNameAndUserId(String name, Long userId);
    
    /**
     * Check if configuration exists by name and user ID
     */
    Mono<Boolean> existsByNameAndUserId(String name, Long userId);
}
