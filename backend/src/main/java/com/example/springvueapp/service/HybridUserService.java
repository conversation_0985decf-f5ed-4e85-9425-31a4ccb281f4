package com.example.springvueapp.service;

import com.example.springvueapp.entity.UserEntity;
import com.example.springvueapp.mapper.UserMapper;
import com.example.springvueapp.model.User;
import com.example.springvueapp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 混合用户服务 - 同时使用 R2DBC 和 JDBC
 * 演示如何在同一个服务中结合使用响应式和传统方法
 */
@Service
public class HybridUserService {

    private final UserRepository userRepository; // R2DBC 响应式仓库
    private final JdbcUserService jdbcUserService; // JDBC 传统服务
    private final UserMapper userMapper; // 实体和DTO转换器

    @Autowired
    public HybridUserService(UserRepository userRepository, JdbcUserService jdbcUserService, UserMapper userMapper) {
        this.userRepository = userRepository;
        this.jdbcUserService = jdbcUserService;
        this.userMapper = userMapper;
    }

    /**
     * 使用 R2DBC 查找用户（优先使用响应式方法）
     */
    public Mono<User> findUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .map(userMapper::toDto)
                .switchIfEmpty(jdbcUserService.findByUsernameReactive(username));
    }

    /**
     * 使用 R2DBC 保存用户（优先使用响应式方法）
     */
    public Mono<User> saveUser(User user) {
        UserEntity userEntity = userMapper.toEntity(user);
        return userRepository.save(userEntity)
                .map(userMapper::toDto)
                .onErrorResume(e -> {
                    // 如果 R2DBC 操作失败，回退到 JDBC
                    return jdbcUserService.saveReactive(user);
                });
    }

    /**
     * 复杂查询示例 - 使用 JDBC 执行复杂 SQL 查询
     * 某些复杂查询可能在 R2DBC 中难以实现或性能不佳
     */
    public Flux<User> findUsersByComplexCriteria(String keyword, boolean activeOnly) {
        // 这里假设这是一个复杂查询，更适合使用 JDBC 实现
        // 在实际应用中，这可能是一个包含多表连接、复杂条件和聚合的查询
        
        // 使用 JDBC 执行查询，然后将结果转换为 Flux
        String sql = "SELECT * FROM users WHERE " +
                     "(username LIKE ? OR email LIKE ? OR full_name LIKE ?) " +
                     (activeOnly ? "AND enabled = true" : "");
        
        // 这里简化了实现，实际中可能需要更复杂的 JDBC 查询
        return Flux.defer(() -> {
            String searchPattern = "%" + keyword + "%";
            return jdbcUserService.findByUsernameReactive(searchPattern)
                    .flux();
        });
    }

    /**
     * 批量操作示例 - 使用 JDBC 进行批量更新
     * 某些批量操作在 JDBC 中可能更高效
     */
    public Mono<Void> performBatchOperation() {
        // 这里可以使用 JdbcTemplate 的 batchUpdate 方法执行批量操作
        // 然后将结果包装为 Mono<Void>
        
        return Mono.fromRunnable(() -> {
            // 执行批量操作的 JDBC 代码
            // 例如：jdbcTemplate.batchUpdate(...)
        });
    }

    /**
     * 事务示例 - 混合使用 R2DBC 和 JDBC 事务
     * 注意：R2DBC 和 JDBC 事务不能直接混合，需要分别管理
     */
    public Mono<User> createUserWithTransaction(User user) {
        // R2DBC 事务
        UserEntity userEntity = userMapper.toEntity(user);
        return userRepository.save(userEntity)
                .map(userMapper::toDto)
                .flatMap(savedUser -> {
                    // 如果需要执行 JDBC 操作，需要在单独的事务中
                    return jdbcUserService.saveReactive(savedUser);
                });
    }
}
