package com.example.springvueapp.entity;

import com.example.springvueapp.sandbox.SandboxStatus;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * MCP 服务器实例的数据库实体类
 * 表示运行中的 MCP 服务器实例
 */
@Table("mcp_server_instances")
public class McpServerInstanceEntity {

    @Id
    private Long id;

    private Long configurationId;

    private String sandboxId; // 沙箱实例的ID

    private SandboxStatus status;

    private String sandboxType; // 例如："docker", "kubernetes"

    private LocalDateTime startedAt;

    private LocalDateTime stoppedAt;

    private String errorMessage;

    private Long userId; // 此实例的所有者

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public McpServerInstanceEntity() {
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getConfigurationId() { return configurationId; }
    public void setConfigurationId(Long configurationId) { this.configurationId = configurationId; }

    public String getSandboxId() { return sandboxId; }
    public void setSandboxId(String sandboxId) { this.sandboxId = sandboxId; }

    public SandboxStatus getStatus() { return status; }
    public void setStatus(SandboxStatus status) { this.status = status; }

    public String getSandboxType() { return sandboxType; }
    public void setSandboxType(String sandboxType) { this.sandboxType = sandboxType; }

    public LocalDateTime getStartedAt() { return startedAt; }
    public void setStartedAt(LocalDateTime startedAt) { this.startedAt = startedAt; }

    public LocalDateTime getStoppedAt() { return stoppedAt; }
    public void setStoppedAt(LocalDateTime stoppedAt) { this.stoppedAt = stoppedAt; }

    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    /**
     * McpServerInstanceEntity 的构建器类
     */
    public static class Builder {
        private McpServerInstanceEntity instance = new McpServerInstanceEntity();

        public Builder id(Long id) {
            instance.setId(id);
            return this;
        }

        public Builder configurationId(Long configurationId) {
            instance.setConfigurationId(configurationId);
            return this;
        }

        public Builder sandboxId(String sandboxId) {
            instance.setSandboxId(sandboxId);
            return this;
        }

        public Builder status(SandboxStatus status) {
            instance.setStatus(status);
            return this;
        }

        public Builder sandboxType(String sandboxType) {
            instance.setSandboxType(sandboxType);
            return this;
        }

        public Builder startedAt(LocalDateTime startedAt) {
            instance.setStartedAt(startedAt);
            return this;
        }

        public Builder stoppedAt(LocalDateTime stoppedAt) {
            instance.setStoppedAt(stoppedAt);
            return this;
        }

        public Builder errorMessage(String errorMessage) {
            instance.setErrorMessage(errorMessage);
            return this;
        }

        public Builder userId(Long userId) {
            instance.setUserId(userId);
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            instance.setCreatedAt(createdAt);
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            instance.setUpdatedAt(updatedAt);
            return this;
        }

        public McpServerInstanceEntity build() {
            return instance;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
