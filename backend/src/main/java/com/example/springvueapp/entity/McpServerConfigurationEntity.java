package com.example.springvueapp.entity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * MCP 服务器配置的数据库实体类
 * 用于与数据库交互，复杂类型以JSON字符串形式存储
 */
@Table("mcp_server_configurations")
public class McpServerConfigurationEntity {

    @Id
    private Long id;

    @NotBlank(message = "Name is required")
    private String name;

    private String description;

    @NotBlank(message = "Command is required")
    private String command;

    private String arguments; // JSON string of List<String>

    private String environment; // JSON string of Map<String, String>

    private String workingDirectory;

    @NotBlank(message = "Docker image is required")
    private String dockerImage;

    private String resourceLimits; // JSON string of ResourceLimits

    private String networkConfig; // JSON string of NetworkConfig

    private String volumeMounts; // JSON string of List<VolumeMount>

    private Integer timeoutSeconds;

    private Boolean autoRestart = false;

    private Boolean enabled = true;

    @NotNull
    private Long userId; // 此配置的所有者

    // 传输协议相关字段
    private String transportType = "stdio"; // 传输协议类型

    private String serverUrl; // SSE/WebSocket服务器URL

    private String transportHeaders; // JSON string of Map<String, String>

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    // 私有构造函数，只能通过构建器创建实例
    private McpServerConfigurationEntity() {}

    // 私有构造函数，通过构建器传入参数
    private McpServerConfigurationEntity(Builder builder) {
        this.id = builder.id;
        this.name = builder.name;
        this.description = builder.description;
        this.command = builder.command;
        this.arguments = builder.arguments;
        this.environment = builder.environment;
        this.workingDirectory = builder.workingDirectory;
        this.dockerImage = builder.dockerImage;
        this.resourceLimits = builder.resourceLimits;
        this.networkConfig = builder.networkConfig;
        this.volumeMounts = builder.volumeMounts;
        this.timeoutSeconds = builder.timeoutSeconds;
        this.autoRestart = builder.autoRestart;
        this.enabled = builder.enabled;
        this.userId = builder.userId;
        this.createdAt = builder.createdAt;
        this.updatedAt = builder.updatedAt;
    }

    // 静态构建器类
    public static class Builder {
        private Long id;
        private String name;
        private String description;
        private String command;
        private String arguments;
        private String environment;
        private String workingDirectory;
        private String dockerImage;
        private String resourceLimits;
        private String networkConfig;
        private String volumeMounts;
        private Integer timeoutSeconds;
        private Boolean autoRestart = false;
        private Boolean enabled = true;
        private Long userId;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder description(String description) {
            this.description = description;
            return this;
        }

        public Builder command(String command) {
            this.command = command;
            return this;
        }

        public Builder arguments(String arguments) {
            this.arguments = arguments;
            return this;
        }

        public Builder environment(String environment) {
            this.environment = environment;
            return this;
        }

        public Builder workingDirectory(String workingDirectory) {
            this.workingDirectory = workingDirectory;
            return this;
        }

        public Builder dockerImage(String dockerImage) {
            this.dockerImage = dockerImage;
            return this;
        }

        public Builder resourceLimits(String resourceLimits) {
            this.resourceLimits = resourceLimits;
            return this;
        }

        public Builder networkConfig(String networkConfig) {
            this.networkConfig = networkConfig;
            return this;
        }

        public Builder volumeMounts(String volumeMounts) {
            this.volumeMounts = volumeMounts;
            return this;
        }

        public Builder timeoutSeconds(Integer timeoutSeconds) {
            this.timeoutSeconds = timeoutSeconds;
            return this;
        }

        public Builder autoRestart(Boolean autoRestart) {
            this.autoRestart = autoRestart;
            return this;
        }

        public Builder enabled(Boolean enabled) {
            this.enabled = enabled;
            return this;
        }

        public Builder userId(Long userId) {
            this.userId = userId;
            return this;
        }

        public Builder createdAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public Builder updatedAt(LocalDateTime updatedAt) {
            this.updatedAt = updatedAt;
            return this;
        }

        public McpServerConfigurationEntity build() {
            return new McpServerConfigurationEntity(this);
        }
    }

    public static Builder builder() {
        return new Builder();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getCommand() { return command; }
    public void setCommand(String command) { this.command = command; }

    public String getArguments() { return arguments; }
    public void setArguments(String arguments) { this.arguments = arguments; }

    public String getEnvironment() { return environment; }
    public void setEnvironment(String environment) { this.environment = environment; }

    public String getWorkingDirectory() { return workingDirectory; }
    public void setWorkingDirectory(String workingDirectory) { this.workingDirectory = workingDirectory; }

    public String getDockerImage() { return dockerImage; }
    public void setDockerImage(String dockerImage) { this.dockerImage = dockerImage; }

    public String getResourceLimits() { return resourceLimits; }
    public void setResourceLimits(String resourceLimits) { this.resourceLimits = resourceLimits; }

    public String getNetworkConfig() { return networkConfig; }
    public void setNetworkConfig(String networkConfig) { this.networkConfig = networkConfig; }

    public String getVolumeMounts() { return volumeMounts; }
    public void setVolumeMounts(String volumeMounts) { this.volumeMounts = volumeMounts; }

    public Integer getTimeoutSeconds() { return timeoutSeconds; }
    public void setTimeoutSeconds(Integer timeoutSeconds) { this.timeoutSeconds = timeoutSeconds; }

    public Boolean getAutoRestart() { return autoRestart; }
    public void setAutoRestart(Boolean autoRestart) { this.autoRestart = autoRestart; }

    public Boolean getEnabled() { return enabled; }
    public void setEnabled(Boolean enabled) { this.enabled = enabled; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
