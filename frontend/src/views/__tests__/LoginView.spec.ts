import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import LoginView from '../LoginView.vue'
import { useAuthStore } from '@/store/auth'

// 创建模拟的路由实例
const mockPush = vi.fn()
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/login', name: 'login', component: LoginView },
    { path: '/mcp', name: 'mcp', component: { template: '<div>MCP</div>' } }
  ]
})

// 模拟路由的push方法
vi.spyOn(router, 'push').mockImplementation(mockPush)

// 模拟 Pinia 存储
const mockLogin = vi.fn().mockResolvedValue(true)
const mockRegister = vi.fn().mockResolvedValue(true)
vi.mock('@/store/auth', () => ({
  useAuthStore: vi.fn(() => ({
    login: mockLogin,
    register: mockRegister,
    error: null,
    loading: false
  }))
}))

describe('LoginView', () => {
  beforeEach(() => {
    // 创建一个新的 Pinia 实例
    setActivePinia(createPinia())

    // 重置路由
    router.push('/login')

    // 清除所有模拟
    vi.clearAllMocks()
    mockPush.mockClear()
    mockLogin.mockClear()
    mockRegister.mockClear()
  })

  it('renders login form by default', () => {
    const wrapper = mount(LoginView, {
      global: {
        plugins: [router]
      }
    })

    // 验证标题
    expect(wrapper.find('h1').text()).toBe('登录')

    // 验证表单字段
    expect(wrapper.find('#username').exists()).toBe(true)
    expect(wrapper.find('#password').exists()).toBe(true)
    expect(wrapper.find('#email').exists()).toBe(false)
    expect(wrapper.find('#fullName').exists()).toBe(false)

    // 验证按钮文本
    expect(wrapper.find('button[type="submit"]').text()).toBe('登录')
  })

  it('toggles between login and register forms', async () => {
    const wrapper = mount(LoginView, {
      global: {
        plugins: [router]
      }
    })

    // 初始状态是登录表单
    expect(wrapper.find('h1').text()).toBe('登录')

    // 点击切换链接
    await wrapper.find('.text-center a').trigger('click')

    // 现在应该是注册表单
    expect(wrapper.find('h1').text()).toBe('注册')
    expect(wrapper.find('#email').exists()).toBe(true)
    expect(wrapper.find('#fullName').exists()).toBe(true)

    // 再次点击切换链接
    await wrapper.find('.text-center a').trigger('click')

    // 应该回到登录表单
    expect(wrapper.find('h1').text()).toBe('登录')
  })

  it('submits login form and redirects on success', async () => {
    const wrapper = mount(LoginView, {
      global: {
        plugins: [router]
      }
    })

    // 填写表单
    await wrapper.find('#username').setValue('testuser')
    await wrapper.find('#password').setValue('password')

    // 提交表单
    await wrapper.find('form').trigger('submit.prevent')

    // 等待异步操作完成
    await wrapper.vm.$nextTick()

    // 验证 store 方法被调用
    expect(mockLogin).toHaveBeenCalledWith('testuser', 'password')

    // 验证路由跳转到MCP页面（根据实际组件逻辑）
    expect(mockPush).toHaveBeenCalledWith('/mcp')
  })

  it('submits register form and switches to login on success', async () => {
    const wrapper = mount(LoginView, {
      global: {
        plugins: [router]
      }
    })

    // 切换到注册表单
    await wrapper.find('.text-center a').trigger('click')

    // 填写表单
    await wrapper.find('#username').setValue('newuser')
    await wrapper.find('#email').setValue('<EMAIL>')
    await wrapper.find('#password').setValue('password')
    await wrapper.find('#fullName').setValue('New User')

    // 提交表单
    await wrapper.find('form').trigger('submit.prevent')

    // 等待异步操作完成
    await wrapper.vm.$nextTick()

    // 验证 store 方法被调用
    expect(mockRegister).toHaveBeenCalledWith({
      username: 'newuser',
      email: '<EMAIL>',
      password: 'password',
      fullName: 'New User'
    })

    // 验证切换回登录表单
    expect(wrapper.find('h1').text()).toBe('登录')
  })
})
